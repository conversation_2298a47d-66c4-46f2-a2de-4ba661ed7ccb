import * as vscode from 'vscode';
import { ContextEngine } from './core/contextEngine';
import { CompletionProvider } from './providers/completionProvider';
import { ChatPanel } from './ui/chatPanel';
import { AgentMode } from './core/agentMode';
import { SecurityManager } from './core/securityManager';
import { MorpheusSidebarProvider } from './ui/sidebarProvider';

import { ConfigurationManager } from './core/configurationManager';

let contextEngine: ContextEngine;
let completionProvider: CompletionProvider;
let chatPanel: ChatPanel;
let agentMode: AgentMode;
let securityManager: SecurityManager;
let configManager: ConfigurationManager;

export async function activate(context: vscode.ExtensionContext) {
    console.log('Morpheus extension is now active!');

    // Initialize core components
    configManager = new ConfigurationManager(context);
    securityManager = new SecurityManager(context);
    contextEngine = new ContextEngine(context, securityManager);
    completionProvider = new CompletionProvider(contextEngine);
    agentMode = new AgentMode(context, contextEngine, securityManager);

    // Initialize sidebar providers
    const sidebarProvider = new MorpheusSidebarProvider(context.extensionUri, contextEngine, agentMode);

    // Store context globally for AI service
    (global as any).morpheusContext = context;

    // Set context for when chat is enabled
    vscode.commands.executeCommand('setContext', 'morpheus.chatEnabled', true);

    // Register completion providers for supported languages
    const supportedLanguages = configManager.getSupportedLanguages();
    for (const language of supportedLanguages) {
        const provider = vscode.languages.registerInlineCompletionItemProvider(
            { language },
            completionProvider
        );
        context.subscriptions.push(provider);
    }

    // Register webview providers
    console.log('Registering Morpheus webview providers...');
    try {
        context.subscriptions.push(
            vscode.window.registerWebviewViewProvider(MorpheusSidebarProvider.viewType, sidebarProvider)
        );
        console.log('Morpheus webview providers registered successfully');
    } catch (error) {
        console.error('Failed to register Morpheus webview providers:', error);
    }

    // Register commands
    const commands = [
        vscode.commands.registerCommand('morpheus.openChat', () => {
            ChatPanel.createOrShow(context, contextEngine, securityManager, agentMode);
        }),
        
        vscode.commands.registerCommand('morpheus.toggleAgentMode', () => {
            agentMode.toggle();
        }),

        vscode.commands.registerCommand('morpheus.refreshIndex', async () => {
            vscode.window.showInformationMessage('Morpheus: Refreshing workspace index...');
            await contextEngine.refreshIndex();
        }),

        vscode.commands.registerCommand('morpheus.showIndexStatus', () => {
            const allSymbols = contextEngine.getAllSymbols();
            const fileCount = contextEngine.getIndexedFileCount();
            const workspaceStructure = contextEngine.getWorkspaceStructure();

            const message = `Morpheus Index Status:
• Files indexed: ${fileCount}
• Symbols found: ${allSymbols.length}
• Project type: ${workspaceStructure?.moduleType || 'unknown'}
• Workspace: ${vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || 'none'}`;

            vscode.window.showInformationMessage(message, { modal: true });
        }),

        vscode.commands.registerCommand('morpheus.openSidebar', async () => {
            try {
                // Try multiple ways to open the sidebar
                console.log('Attempting to open Morpheus sidebar...');

                // Method 1: Direct view command
                try {
                    await vscode.commands.executeCommand('workbench.view.extension.morpheus');
                    console.log('Opened via workbench.view.extension.morpheus');
                } catch (e1) {
                    console.log('Method 1 failed:', e1);

                    // Method 2: Focus specific view
                    try {
                        await vscode.commands.executeCommand('morpheus.chatView.focus');
                        console.log('Opened via morpheus.chatView.focus');
                    } catch (e2) {
                        console.log('Method 2 failed:', e2);

                        // Method 3: Show view
                        try {
                            await vscode.commands.executeCommand('workbench.action.openView', 'morpheus.chatView');
                            console.log('Opened via workbench.action.openView');
                        } catch (e3) {
                            console.log('Method 3 failed:', e3);
                            throw new Error('All methods failed');
                        }
                    }
                }

                vscode.window.showInformationMessage('Morpheus sidebar opened!');
            } catch (error) {
                vscode.window.showErrorMessage(`Failed to open Morpheus sidebar: ${error}`);
                console.error('Failed to open sidebar:', error);
            }
        }),

        vscode.commands.registerCommand('morpheus.showActivityBar', async () => {
            try {
                // Force show the activity bar icon
                await vscode.commands.executeCommand('workbench.action.toggleActivityBarVisibility');
                await vscode.commands.executeCommand('workbench.action.toggleActivityBarVisibility');
                vscode.window.showInformationMessage('Activity bar refreshed. Look for the 👁️ Morpheus icon!');
            } catch (error) {
                vscode.window.showErrorMessage(`Failed to refresh activity bar: ${error}`);
            }
        }),
        
        vscode.commands.registerCommand('morpheus.refreshContext', async () => {
            await contextEngine.refreshIndex();
            vscode.window.showInformationMessage('Context engine refreshed successfully');
        }),
        
        vscode.commands.registerCommand('morpheus.reviewChanges', () => {
            agentMode.showPendingChanges();
        }),
        
        vscode.commands.registerCommand('morpheus.configureSettings', () => {
            vscode.commands.executeCommand('workbench.action.openSettings', 'morpheus');
        })
    ];

    context.subscriptions.push(...commands);

    // Initialize context engine
    await contextEngine.initialize();

    // Auto-open the Morpheus sidebar with multiple attempts
    const autoOpenSidebar = async () => {
        console.log('Attempting to auto-open Morpheus sidebar...');

        const attempts = [
            { name: 'workbench.view.extension.morpheus', cmd: () => vscode.commands.executeCommand('workbench.view.extension.morpheus') },
            { name: 'morpheus.chatView.focus', cmd: () => vscode.commands.executeCommand('morpheus.chatView.focus') },
            { name: 'workbench.action.openView morpheus.chatView', cmd: () => vscode.commands.executeCommand('workbench.action.openView', 'morpheus.chatView') },
            { name: 'setContext morpheus', cmd: () => vscode.commands.executeCommand('setContext', 'morpheus:enabled', true) },
            { name: 'workbench.action.focusActivityBar', cmd: () => vscode.commands.executeCommand('workbench.action.focusActivityBar') }
        ];

        for (let i = 0; i < attempts.length; i++) {
            try {
                console.log(`Trying auto-open method ${i + 1}: ${attempts[i].name}`);
                await attempts[i].cmd();
                console.log(`✅ Morpheus sidebar opened automatically (method ${i + 1}: ${attempts[i].name})`);
                return true;
            } catch (error) {
                console.log(`❌ Auto-open method ${i + 1} (${attempts[i].name}) failed:`, error);
            }
        }
        console.log('❌ All auto-open methods failed, sidebar will be available manually');
        return false;
    };

    // Try multiple times with increasing delays and show results
    setTimeout(async () => {
        const success = await autoOpenSidebar();
        if (!success) {
            console.log('💡 Try: Ctrl+Shift+P → "View: Show Chat" to open Morpheus manually');
        }
    }, 1000);

    setTimeout(() => autoOpenSidebar(), 3000);
    setTimeout(() => autoOpenSidebar(), 5000);

    // Show welcome message on first activation
    const isFirstActivation = context.globalState.get('morpheus.firstActivation', true);
    if (isFirstActivation) {
        await showWelcomeMessage(context);
        context.globalState.update('morpheus.firstActivation', false);
    }

    // Listen for configuration changes
    const configChangeListener = vscode.workspace.onDidChangeConfiguration(event => {
        if (event.affectsConfiguration('morpheus')) {
            handleConfigurationChange();
        }
    });
    context.subscriptions.push(configChangeListener);

    console.log('Morpheus extension activated successfully');
}

export function deactivate() {
    console.log('Morpheus extension is being deactivated');
    
    // Clean up resources
    if (contextEngine) {
        contextEngine.dispose();
    }
    if (ChatPanel.currentPanel) {
        ChatPanel.currentPanel.dispose();
    }
    if (agentMode) {
        agentMode.dispose();
    }
}

async function showWelcomeMessage(context: vscode.ExtensionContext) {
    const action = await vscode.window.showInformationMessage(
        'Welcome to Morpheus! Your AI-powered development assistant is ready.',
        'Open Chat',
        'Configure Settings',
        'Learn More'
    );

    switch (action) {
        case 'Open Chat':
            vscode.commands.executeCommand('morpheus.openChat');
            break;
        case 'Configure Settings':
            vscode.commands.executeCommand('morpheus.configureSettings');
            break;
        case 'Learn More':
            vscode.env.openExternal(vscode.Uri.parse('https://github.com/morpheus-dev/morpheus-vscode'));
            break;
    }
}

async function handleConfigurationChange() {
    const config = vscode.workspace.getConfiguration('morpheus');
    
    // Refresh context engine if relevant settings changed
    if (config.get('enableContextEngine')) {
        await contextEngine.refreshIndex();
    }
    
    // Update completion provider settings
    completionProvider.updateConfiguration();
    
    // Update chat panel settings
    if (ChatPanel.currentPanel) {
        ChatPanel.currentPanel.updateConfiguration();
    }
    
    console.log('Configuration updated');
}
