# Morpheus Extension - Troubleshooting Guide

## 🚀 Quick Start (Fixed F5 Issue)

The F5 launch issue has been resolved! You now have multiple launch options:

### Option 1: Run Extension (Recommended)
1. Press `F5` or go to Run and Debug panel
2. Select **"Run Extension"** from the dropdown
3. Click the green play button

### Option 2: Run Extension (with compile)
1. Select **"Run Extension (with compile)"** from the dropdown
2. This will automatically compile before launching

### Option 3: Manual Compile + Run
1. Run `npm run compile` in terminal first
2. Then use **"Run Extension"** option

## 🔧 Common Issues & Solutions

### Issue: "Could not find the task" Error
**Solution**: Use the **"Run Extension"** configuration (without preLaunchTask)

### Issue: Extension doesn't load
**Symptoms**: No Morpheus commands appear in Command Palette
**Solutions**:
1. Check VS Code Developer Console (`Help > Toggle Developer Tools`)
2. Look for error messages in the Console tab
3. Ensure `dist/extension.js` exists (run `npm run compile`)

### Issue: "Module not found" errors
**Solutions**:
1. Run `npm install` to ensure all dependencies are installed
2. Run `npm run compile` to rebuild the extension
3. Check that Node.js version is compatible (Node 16+ recommended)

### Issue: Chat panel doesn't open
**Symptoms**: Command runs but no panel appears
**Solutions**:
1. Check if webview is blocked by VS Code settings
2. Try closing and reopening VS Code
3. Check Developer Console for webview errors

### Issue: Code completions not working
**Symptoms**: No inline suggestions appear
**Solutions**:
1. Ensure you're in a supported file type (.py, .vue, .ts, .js)
2. Check that `morpheus.enableInlineCompletions` is true in settings
3. Verify the context engine is running (check status bar)

### Issue: API key not working
**Symptoms**: "Invalid API key" or authentication errors
**Solutions**:
1. Ensure API key starts with "xai-"
2. Re-enter API key via Command Palette: "Morpheus: Configure Settings"
3. Check xAI Grok API service status

## 🛠️ Development Setup Verification

Run this checklist to ensure everything is set up correctly:

```bash
# 1. Check Node.js version (should be 16+)
node --version

# 2. Install dependencies
npm install

# 3. Compile the extension
npm run compile

# 4. Verify compiled files exist
ls -la dist/extension.js
ls -la media/chat.css
ls -la media/chat.js

# 5. Run the test script
node scripts/test-extension.js
```

## 📋 Launch Configurations Available

1. **Run Extension**: Basic launch without pre-compilation
2. **Run Extension (with compile)**: Compiles first, then launches
3. **Extension Tests**: Runs the test suite

## 🔍 Debugging Tips

### Enable Verbose Logging
1. Open VS Code settings
2. Search for "morpheus"
3. Enable debug logging if available

### Check Extension Host Logs
1. In Extension Development Host window
2. Go to `Help > Toggle Developer Tools`
3. Check Console tab for errors

### Verify File Structure
```
morpheus/
├── dist/extension.js          ✅ Should exist after compile
├── media/chat.css            ✅ Should exist
├── media/chat.js             ✅ Should exist
├── package.json              ✅ Extension manifest
└── .vscode/launch.json       ✅ Launch configurations
```

## 🚨 Emergency Reset

If nothing works, try this complete reset:

```bash
# 1. Clean everything
rm -rf node_modules/
rm -rf dist/
rm -rf out/

# 2. Reinstall
npm install

# 3. Recompile
npm run compile

# 4. Try launching again with F5
```

## 📞 Getting Help

If you're still having issues:

1. **Check the Console**: Look for specific error messages
2. **Verify Prerequisites**: Node.js 16+, VS Code 1.92.0+
3. **Test with Sample Files**: Use the provided `sample/` directory files
4. **Check Network**: Ensure internet connection for API calls

## ✅ Success Indicators

You'll know everything is working when:
- ✅ F5 launches a new VS Code window
- ✅ Command Palette shows Morpheus commands
- ✅ Status bar shows "🤖 Agent: OFF" 
- ✅ Chat panel opens with "Morpheus: Open Chat"
- ✅ Code completions appear in Python/Vue files

## 🎯 Next Steps After Setup

1. **Configure API Key**: Enter your xAI Grok API key
2. **Test Chat**: Ask a simple question about your code
3. **Try Completions**: Open a Python file and start typing
4. **Enable Agent Mode**: Toggle agent mode and give it a task
5. **Explore Settings**: Customize the extension to your needs

The extension is now ready to boost your development productivity! 🚀
