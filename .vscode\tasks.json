{"version": "2.0.0", "tasks": [{"type": "npm", "script": "compile", "group": "build", "label": "npm: compile", "presentation": {"panel": "shared", "reveal": "silent"}, "problemMatcher": "$tsc"}, {"type": "npm", "script": "compile-tests", "group": "build", "label": "npm: compile-tests", "presentation": {"panel": "shared", "reveal": "silent"}, "problemMatcher": "$tsc"}, {"type": "npm", "script": "watch", "group": "build", "presentation": {"panel": "shared", "reveal": "never"}, "isBackground": true, "problemMatcher": "$tsc-watch"}, {"type": "npm", "script": "lint", "group": "build", "presentation": {"panel": "shared", "reveal": "silent"}, "problemMatcher": "$eslint-stylish"}]}