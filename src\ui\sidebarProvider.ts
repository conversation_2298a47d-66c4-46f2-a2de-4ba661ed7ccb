import * as vscode from 'vscode';
import { ContextEngine } from '../core/contextEngine';
import { AgentMode } from '../core/agentMode';
import { AIService, ChatMessage } from '../services/aiService';

export class MorpheusSidebarProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'morpheus.chatView';
    private _view?: vscode.WebviewView;
    private contextEngine: ContextEngine;
    private agentMode: AgentMode;
    private aiService: AIService;
    private chatHistory: ChatMessage[] = [];

    constructor(
        private readonly _extensionUri: vscode.Uri,
        contextEngine: ContextEngine,
        agentMode: AgentMode
    ) {
        this.contextEngine = contextEngine;
        this.agentMode = agentMode;
        this.aiService = new AIService();
    }

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this._extensionUri]
        };

        // Set HTML content with extensive debugging
        console.log('🔧 Starting webview setup...');

        const htmlContent = this._getHtmlForWebview(webviewView.webview);
        console.log('🔧 Generated HTML content length:', htmlContent.length);
        console.log('🔧 HTML preview:', htmlContent.substring(0, 200) + '...');

        try {
            webviewView.webview.html = htmlContent;
            console.log('✅ HTML set successfully');
        } catch (error) {
            console.error('❌ Failed to set HTML:', error);
        }

        // Auto-show the view when it's created
        try {
            webviewView.show?.(true);
            console.log('✅ View show() called');
        } catch (error) {
            console.error('❌ Failed to show view:', error);
        }

        console.log('🔧 Morpheus chat view setup complete');

        // Debug: Check webview state multiple times
        setTimeout(() => {
            console.log('🔧 [1s] Webview visible:', webviewView.visible);
            console.log('🔧 [1s] Webview HTML length:', webviewView.webview.html.length);
            console.log('🔧 [1s] Webview title:', webviewView.title);
        }, 1000);

        setTimeout(() => {
            console.log('🔧 [3s] Webview visible:', webviewView.visible);
            console.log('🔧 [3s] Webview HTML length:', webviewView.webview.html.length);
        }, 3000);

        // Handle messages from the webview
        webviewView.webview.onDidReceiveMessage(async (data) => {
            switch (data.type) {
                case 'sendMessage':
                    await this.handleUserMessage(data.message);
                    break;
                case 'clearChat':
                    this.clearChat();
                    break;
                case 'toggleAgent':
                    this.toggleAgentMode();
                    break;
                case 'refreshIndex':
                    await this.refreshWorkspaceIndex();
                    break;
            }
        });

        // Keep the view visible
        webviewView.show?.(true);
    }

    private async handleUserMessage(message: string) {
        console.log(`🚀🚀🚀 SIDEBAR CHAT: handleUserMessage called with: "${message}"`);

        // Add user message to history
        this.chatHistory.push({ role: 'user', content: message });

        // Send user message to webview immediately
        this._view?.webview.postMessage({
            type: 'userMessage',
            message: message,
            timestamp: new Date().toISOString()
        });

        // Show typing indicator
        this._view?.webview.postMessage({
            type: 'typingStart'
        });

        try {
            // If Agent Mode is enabled, treat ALL messages as tasks
            const agentEnabled = this.agentMode.enabled;
            console.log(`🚀 SIDEBAR AGENT CHECK: agentEnabled=${agentEnabled}, message="${message}"`);

            if (agentEnabled) {
                console.log(`🚀 SIDEBAR ROUTING TO AGENT MODE (Agent Mode ON - treating all messages as tasks)`);
                await this.handleAgentTask(message);
                return;
            }
            console.log(`🚀 SIDEBAR ROUTING TO NORMAL CHAT (Agent Mode OFF)`);

            // Get comprehensive workspace context
            const contextInfo = await this.buildComprehensiveContext(message);

            // Prepare enhanced system message with context
            const systemMessage: ChatMessage = {
                role: 'system',
                content: `You are Morpheus, an AI-powered development assistant with full access to the user's workspace and codebase.

🔍 IMPORTANT: You can see all the user's code and workspace context below. When they ask about their code, analyze what you can see directly. DO NOT ask them to paste code - you already have complete access to their workspace.

${contextInfo}

You help with:
1. Code explanation and debugging (analyze the code you can see above)
2. Generating Python/Vue scripts for business tasks
3. Code refactoring and optimization
4. Creating unit tests
5. Generating reports and data processing scripts

Guidelines:
- Analyze code directly from the workspace context provided above
- Reference specific files, functions, and code snippets you can see
- Provide detailed explanations based on the actual code structure
- Point out specific issues with file/line references when possible
- Use the workspace structure and project type for contextually appropriate advice
- The user expects you to understand their codebase without them explaining it
- Provide practical, actionable solutions based on what you can see`
            };

            // Prepare messages for AI (simplified for sidebar)
            const messages: ChatMessage[] = [
                systemMessage as ChatMessage,
                ...this.chatHistory.slice(-5) // Keep last 5 messages for context
            ];

            // Generate AI response using the real AI service
            const response = await this.generateAIResponse(messages);

            if (response) {
                // Add AI response to history
                this.chatHistory.push({ role: 'assistant', content: response });

                // Send AI response to webview
                this._view?.webview.postMessage({
                    type: 'aiResponse',
                    message: response,
                    timestamp: new Date().toISOString()
                });
            } else {
                // Handle case where AI service fails
                this._view?.webview.postMessage({
                    type: 'error',
                    message: 'Sorry, I couldn\'t generate a response. Please check your API key configuration.',
                    timestamp: new Date().toISOString()
                });
            }
        } catch (error) {
            console.error('Error generating response:', error);
            this._view?.webview.postMessage({
                type: 'error',
                message: 'Sorry, I encountered an error processing your request.',
                timestamp: new Date().toISOString()
            });
        } finally {
            // Hide typing indicator
            this._view?.webview.postMessage({
                type: 'typingEnd'
            });
        }
    }

    private async generateAIResponse(messages: ChatMessage[]): Promise<string | null> {
        try {
            // Use the real AI service to generate responses with step visibility
            const response = await this.aiService.generateChatResponse(messages, {
                maxTokens: 2000,
                temperature: 0.7,
                showSteps: true,
                onStep: (step: string) => {
                    // Send step updates to the webview
                    this._view?.webview.postMessage({
                        type: 'processingStep',
                        step: step,
                        timestamp: new Date().toISOString()
                    });
                }
            });

            return response;
        } catch (error) {
            console.error('AI service error:', error);
            return null;
        }
    }

    private async buildComprehensiveContext(userMessage: string): Promise<string> {
        console.log(`🚀 SIDEBAR: buildComprehensiveContext called with message: "${userMessage}"`);
        let contextInfo = '';

        // 1. Workspace Overview
        const workspaceStructure = this.contextEngine.getWorkspaceStructure();
        const indexedFileCount = this.contextEngine.getIndexedFileCount();

        contextInfo += `=== WORKSPACE CONTEXT ===\n`;
        contextInfo += `Project Type: ${workspaceStructure?.moduleType || 'unknown'}\n`;
        contextInfo += `Files Indexed: ${indexedFileCount}\n`;
        contextInfo += `Workspace: ${vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || 'none'}\n`;

        // 2. Task-Relevant Context (prioritize files based on user message)
        console.log(`🔍 SIDEBAR: Getting task-relevant files for message: "${userMessage}"`);
        const taskRelevantFiles = this.getTaskRelevantFiles(userMessage);
        console.log(`🔍 SIDEBAR: Found ${taskRelevantFiles.length} task-relevant files:`, taskRelevantFiles.map(f => `${f.filePath} (${f.score.toFixed(2)})`));

        if (taskRelevantFiles.length > 0) {
            contextInfo += `\n=== RELEVANT FILES FOR YOUR REQUEST ===\n`;
            taskRelevantFiles.forEach(fileInfo => {
                contextInfo += `\nFile: ${fileInfo.filePath} (relevance: ${fileInfo.score.toFixed(2)})\n`;
                contextInfo += `Language: ${fileInfo.language}\n`;
                contextInfo += `Content:\n\`\`\`${fileInfo.language}\n${fileInfo.content.substring(0, 3000)}${fileInfo.content.length > 3000 ? '\n... (truncated)' : ''}\n\`\`\`\n`;
            });
        }

        return contextInfo;
    }

    private getTaskRelevantFiles(userMessage: string): Array<{filePath: string, language: string, content: string, score: number}> {
        const lowerMessage = userMessage.toLowerCase();
        const allFiles = this.contextEngine.getAllSymbols();
        console.log(`🔍 SIDEBAR: Total symbols from context engine: ${allFiles.length}`);
        console.log(`🔍 SIDEBAR: All file paths:`, allFiles.map(s => s.filePath));

        const relevantFiles: Array<{filePath: string, score: number}> = [];

        // Score files based on user message keywords
        for (const symbol of allFiles) {
            let score = 0;
            const fileName = require('path').basename(symbol.filePath).toLowerCase();
            console.log(`🔍 SIDEBAR: Scoring file: ${fileName} for message: "${lowerMessage}"`);

            // Check for direct name matches
            if (lowerMessage.includes('login') && fileName.includes('login')) {
                score += 1.0;
            }
            if (lowerMessage.includes('vue') && fileName.endsWith('.vue')) {
                score += 0.8;
            }
            if (lowerMessage.includes('component') && fileName.includes('component')) {
                score += 0.7;
            }
            if (lowerMessage.includes('auth') && (fileName.includes('auth') || fileName.includes('login'))) {
                score += 0.9;
            }
            if (lowerMessage.includes('app') && fileName.includes('app')) {
                score += 0.6;
            }
            if (lowerMessage.includes('main') && fileName.includes('main')) {
                score += 0.6;
            }
            if (lowerMessage.includes('data') && fileName.includes('data')) {
                score += 0.6;
            }

            // Check for file type relevance
            if (lowerMessage.includes('python') && fileName.endsWith('.py')) {
                score += 0.6;
            }
            if (lowerMessage.includes('flask') && fileName.includes('app')) {
                score += 0.7;
            }
            if (lowerMessage.includes('javascript') && fileName.endsWith('.js')) {
                score += 0.6;
            }

            console.log(`🔍 SIDEBAR: File ${fileName} scored: ${score}`);
            if (score > 0.1) {
                relevantFiles.push({ filePath: symbol.filePath, score });
                console.log(`🔍 SIDEBAR: ✅ Added ${fileName} to relevant files with score ${score}`);
            }
        }

        // Sort by relevance and get top files
        relevantFiles.sort((a, b) => b.score - a.score);
        const topFiles = relevantFiles.slice(0, 5); // Top 5 most relevant files

        // Get file content for relevant files
        const filesWithContent: Array<{filePath: string, language: string, content: string, score: number}> = [];
        for (const {filePath, score} of topFiles) {
            const fileIndex = this.contextEngine.getFileIndex(filePath);
            if (fileIndex) {
                filesWithContent.push({
                    filePath,
                    language: fileIndex.language,
                    content: fileIndex.content,
                    score
                });
            }
        }

        return filesWithContent;
    }

    private isAgentTask(message: string): boolean {
        const taskKeywords = [
            'create', 'generate', 'write', 'build', 'make', 'run', 'execute',
            'script', 'file', 'function', 'class', 'test', 'report'
        ];

        const messageLower = message.toLowerCase();
        return taskKeywords.some(keyword => messageLower.includes(keyword));
    }

    private async handleAgentTask(message: string) {
        // Set up progress callback to show steps in sidebar
        this.agentMode.setProgressCallback((step: string) => {
            this._view?.webview.postMessage({
                type: 'processingStep',
                step: step,
                timestamp: new Date().toISOString()
            });
        });

        // Delegate to agent mode
        try {
            const task = await this.agentMode.executeTask(message);

            // Only show success if the task actually completed successfully
            if (task.status === 'completed') {
                this._view?.webview.postMessage({
                    type: 'agentComplete',
                    message: '🤖 Agent task completed successfully!',
                    timestamp: new Date().toISOString()
                });
            } else {
                // Task failed or was cancelled
                this._view?.webview.postMessage({
                    type: 'error',
                    message: `🤖 Agent task ${task.status}: ${task.error || 'Unknown error'}`,
                    timestamp: new Date().toISOString()
                });
            }
        } catch (error) {
            this._view?.webview.postMessage({
                type: 'error',
                message: `🤖 Agent task failed: ${error}`,
                timestamp: new Date().toISOString()
            });
        }
    }

    private clearChat() {
        this.chatHistory = [];
        this._view?.webview.postMessage({ type: 'clearChat' });
    }

    private toggleAgentMode() {
        this.agentMode.toggle();
        const status = this.agentMode.enabled ? 'enabled' : 'disabled';
        this._view?.webview.postMessage({
            type: 'agentStatus',
            enabled: this.agentMode.enabled,
            message: `AGENT MODE ${status.toUpperCase()}`
        });
    }

    private async refreshWorkspaceIndex() {
        await this.contextEngine.refreshIndex();
        this._view?.webview.postMessage({
            type: 'indexRefreshed',
            message: 'WORKSPACE INDEX REFRESHED'
        });
    }

    private _getHtmlForWebview(webview: vscode.Webview) {
        const nonce = this.getNonce();
        const cacheBuster = Date.now();

        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}';">
            <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
            <meta http-equiv="Pragma" content="no-cache">
            <meta http-equiv="Expires" content="0">
            <title>Morpheus AI Assistant - ${cacheBuster}</title>
            <style>
                :root {
                    --matrix-green: #00ff41;
                    --matrix-dark-green: #008f11;
                    --matrix-black: #0d1117;
                    --matrix-gray: #1c2128;
                    --matrix-light-gray: #30363d;
                }

                body {
                    font-family: 'Courier New', 'Consolas', monospace;
                    font-size: var(--vscode-font-size);
                    color: var(--matrix-green);
                    background: linear-gradient(135deg, var(--matrix-black) 0%, var(--matrix-gray) 100%);
                    margin: 0;
                    padding: 10px;
                    height: 100vh;
                    display: flex;
                    flex-direction: column;
                }

                .header {
                    display: flex;
                    align-items: center;
                    margin-bottom: 10px;
                    padding-bottom: 10px;
                    border-bottom: 2px solid var(--matrix-dark-green);
                    box-shadow: 0 2px 8px rgba(0, 255, 65, 0.1);
                }

                .logo {
                    font-size: 18px;
                    font-weight: bold;
                    margin-right: 10px;
                    color: var(--matrix-green);
                    text-shadow: 0 0 10px rgba(0, 255, 65, 0.5);
                    letter-spacing: 1px;
                }

                .controls {
                    display: flex;
                    gap: 5px;
                    margin-bottom: 10px;
                }

                .btn {
                    background: linear-gradient(135deg, var(--matrix-dark-green) 0%, var(--matrix-green) 100%);
                    color: var(--matrix-black);
                    border: 1px solid var(--matrix-green);
                    padding: 6px 12px;
                    border-radius: 3px;
                    cursor: pointer;
                    font-size: 10px;
                    font-weight: 600;
                    font-family: 'Courier New', monospace;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                    transition: all 0.3s ease;
                }

                .btn:hover {
                    background: linear-gradient(135deg, var(--matrix-green) 0%, var(--matrix-dark-green) 100%);
                    box-shadow: 0 0 10px rgba(0, 255, 65, 0.4);
                    transform: translateY(-1px);
                }

                .btn.agent-enabled {
                    background: var(--matrix-green);
                    color: var(--matrix-black);
                    box-shadow: 0 0 15px rgba(0, 255, 65, 0.6);
                }

                .chat-container {
                    flex: 1;
                    overflow-y: auto;
                    margin-bottom: 10px;
                    border: 1px solid var(--matrix-dark-green);
                    border-radius: 6px;
                    padding: 10px;
                    background: linear-gradient(135deg, var(--matrix-gray) 0%, var(--matrix-light-gray) 100%);
                    box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.3);
                }

                .message {
                    margin-bottom: 10px;
                    padding: 8px;
                    border-radius: 5px;
                    font-family: 'Courier New', monospace;
                    border: 1px solid var(--matrix-dark-green);
                }

                .user-message {
                    background: transparent;
                    color: var(--matrix-green);
                    margin-left: 20px;
                    border: 1px solid var(--matrix-dark-green);
                }

                .ai-message {
                    background: linear-gradient(135deg, var(--matrix-gray) 0%, var(--matrix-light-gray) 100%);
                    color: var(--matrix-green);
                    margin-right: 20px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
                }

                .input-container {
                    display: flex;
                    gap: 5px;
                }

                .message-input {
                    flex: 1;
                    background: var(--matrix-gray);
                    color: var(--matrix-green);
                    border: 1px solid var(--matrix-dark-green);
                    padding: 8px;
                    border-radius: 3px;
                    font-family: 'Courier New', monospace;
                    transition: all 0.3s ease;
                }

                .message-input:focus {
                    outline: none;
                    border-color: var(--matrix-green);
                    box-shadow: 0 0 8px rgba(0, 255, 65, 0.3);
                }

                .send-btn {
                    background: linear-gradient(135deg, var(--matrix-dark-green) 0%, var(--matrix-green) 100%);
                    color: var(--matrix-black);
                    border: 1px solid var(--matrix-green);
                    padding: 8px 12px;
                    border-radius: 3px;
                    cursor: pointer;
                    font-family: 'Courier New', monospace;
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                    transition: all 0.3s ease;
                }

                .send-btn:hover {
                    background: linear-gradient(135deg, var(--matrix-green) 0%, var(--matrix-dark-green) 100%);
                    box-shadow: 0 0 10px rgba(0, 255, 65, 0.4);
                }

                .typing {
                    font-style: italic;
                    opacity: 0.7;
                    color: var(--matrix-green);
                }

                .processing-steps {
                    background: linear-gradient(135deg, var(--matrix-black) 0%, var(--matrix-gray) 100%);
                    border: 2px solid var(--matrix-green);
                    border-radius: 8px;
                    padding: 12px;
                    margin: 12px 0;
                    font-family: 'Courier New', monospace;
                    font-size: 11px;
                    max-height: 200px;
                    overflow-y: auto;
                    box-shadow: 0 0 15px rgba(0, 255, 65, 0.3);
                }

                .steps-header {
                    color: var(--matrix-green);
                    font-weight: bold;
                    margin-bottom: 8px;
                    text-align: center;
                    border-bottom: 1px solid var(--matrix-green);
                    padding-bottom: 6px;
                    text-shadow: 0 0 5px rgba(0, 255, 65, 0.5);
                    letter-spacing: 1px;
                }

                .processing-step {
                    color: var(--matrix-green);
                    padding: 3px 0;
                    opacity: 0.9;
                    animation: fadeInStep 0.5s ease-in;
                    border-left: 2px solid transparent;
                    padding-left: 6px;
                    margin: 2px 0;
                }

                .processing-step:last-child {
                    border-left-color: var(--matrix-green);
                    opacity: 1;
                    font-weight: bold;
                }

                @keyframes fadeInStep {
                    from {
                        opacity: 0;
                        transform: translateX(-5px);
                    }
                    to {
                        opacity: 0.8;
                        transform: translateX(0);
                    }
                }
            </style>
        </head>
        <body>
            <div class="header">
                <div class="logo">MORPHEUS</div>
            </div>

            <div class="controls">
                <button class="btn" id="agentBtn">AGENT</button>
                <button class="btn" id="refreshBtn">REFRESH</button>
                <button class="btn" id="clearBtn">CLEAR</button>
            </div>

            <div class="chat-container" id="chatContainer">
                <div class="message ai-message">
                    <strong>SYSTEM ONLINE</strong><br><br>
                    Hello. I am Morpheus, your AI coding assistant.<br>
                    I can analyze your workspace and assist with:<br>
                    • Code analysis and debugging<br>
                    • Task automation<br>
                    • Context-aware suggestions<br><br>
                    Enter your query below to begin.
                </div>
            </div>

            <div class="input-container">
                <input type="text" class="message-input" id="messageInput" placeholder="ENTER QUERY...">
                <button class="send-btn" id="sendBtn">SEND</button>
            </div>

            <script nonce="${nonce}">
                const vscode = acquireVsCodeApi();
                const chatContainer = document.getElementById('chatContainer');
                const messageInput = document.getElementById('messageInput');
                const sendBtn = document.getElementById('sendBtn');
                const agentBtn = document.getElementById('agentBtn');
                const refreshBtn = document.getElementById('refreshBtn');
                const clearBtn = document.getElementById('clearBtn');

                let agentEnabled = false;

                function addMessage(content, isUser = false) {
                    const messageDiv = document.createElement('div');
                    messageDiv.className = 'message ' + (isUser ? 'user-message' : 'ai-message');
                    messageDiv.innerHTML = content;
                    chatContainer.appendChild(messageDiv);
                    chatContainer.scrollTop = chatContainer.scrollHeight;
                }

                function showProcessingStep(step) {
                    // Find or create the processing steps container
                    let stepsContainer = document.getElementById('processing-steps');
                    if (!stepsContainer) {
                        stepsContainer = document.createElement('div');
                        stepsContainer.id = 'processing-steps';
                        stepsContainer.className = 'processing-steps';
                        stepsContainer.innerHTML = '<div class="steps-header">MORPHEUS AGENT WORKING...</div>';
                        chatContainer.appendChild(stepsContainer);
                    }

                    // Create step element
                    const stepDiv = document.createElement('div');
                    stepDiv.className = 'processing-step';
                    stepDiv.textContent = step;

                    // Add step to container
                    stepsContainer.appendChild(stepDiv);

                    // Auto-scroll to show the latest step
                    chatContainer.scrollTop = chatContainer.scrollHeight;

                    // Keep the last 10 steps visible (remove older ones)
                    const steps = stepsContainer.querySelectorAll('.processing-step');
                    if (steps.length > 10) {
                        steps[0].remove();
                    }
                }

                function clearProcessingSteps() {
                    const stepsContainer = document.getElementById('processing-steps');
                    if (stepsContainer && stepsContainer.parentNode) {
                        stepsContainer.remove();
                    }
                }

                function sendMessage() {
                    const message = messageInput.value.trim();
                    if (message) {
                        vscode.postMessage({
                            type: 'sendMessage',
                            message: message
                        });
                        messageInput.value = '';
                    }
                }

                sendBtn.addEventListener('click', sendMessage);
                messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        sendMessage();
                    }
                });

                agentBtn.addEventListener('click', () => {
                    vscode.postMessage({ type: 'toggleAgent' });
                });

                refreshBtn.addEventListener('click', () => {
                    vscode.postMessage({ type: 'refreshIndex' });
                });

                clearBtn.addEventListener('click', () => {
                    vscode.postMessage({ type: 'clearChat' });
                });

                // Handle messages from extension
                window.addEventListener('message', event => {
                    const message = event.data;
                    switch (message.type) {
                        case 'userMessage':
                            addMessage(message.message, true);
                            break;
                        case 'aiResponse':
                            addMessage(message.message);
                            break;
                        case 'agentComplete':
                            addMessage(message.message);
                            clearProcessingSteps(); // Clear progress when agent task completes
                            break;
                        case 'indexRefreshed':
                            addMessage(message.message);
                            break;
                        case 'agentStatus':
                            agentEnabled = message.enabled;
                            agentBtn.className = 'btn' + (agentEnabled ? ' agent-enabled' : '');
                            agentBtn.textContent = agentEnabled ? 'AGENT ON' : 'AGENT OFF';
                            break;
                        case 'clearChat':
                            chatContainer.innerHTML = '<div class="message ai-message"><strong>SYSTEM RESET</strong><br><br>Chat cleared. Ready for new queries.</div>';
                            clearProcessingSteps(); // Clear progress when chat is cleared
                            break;
                        case 'typingStart':
                            addMessage('<span class="typing">PROCESSING...</span>');
                            break;
                        case 'typingEnd':
                            const typingMessages = chatContainer.querySelectorAll('.typing');
                            typingMessages.forEach(msg => msg.parentElement.remove());
                            break;
                        case 'error':
                            addMessage('ERROR: ' + message.message);
                            clearProcessingSteps(); // Clear progress when there's an error
                            break;
                        case 'processingStep':
                            showProcessingStep(message.step);
                            break;
                    }
                });
            </script>
        </body>
        </html>`;
    }

    private getNonce() {
        let text = '';
        const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        for (let i = 0; i < 32; i++) {
            text += possible.charAt(Math.floor(Math.random() * possible.length));
        }
        return text;
    }
}
