# Morpheus - AI-Powered Development Assistant

Morpheus is an advanced VS Code extension that replicates key features of Augment to reduce business costs through automation and productivity enhancements. It provides intelligent code completions, chat assistance, and autonomous development capabilities.

## Features

### 🚀 Inline Code Completions
- **Context-aware suggestions** for Python, Vue, TypeScript, and JavaScript files
- **200K token context window** for comprehensive code understanding
- **Pattern matching** for business logic and data processing tasks
- **Tree-sitter parsing** for accurate symbol extraction
- Accept completions with `Tab` or `Cmd/Ctrl+→`

### 💬 Chat Interface
- **Reactive Vue.js-based UI** in a dedicated webview panel
- **Natural language queries** for code explanation and debugging
- **Script generation** for business tasks (CSV reports, data processing)
- **Code execution** and insertion directly from chat
- **Context-aware responses** using current workspace information

### 🧠 Context Engine
- **Real-time codebase indexing** with file watching (Chokidar)
- **Symbol extraction** for functions, classes, variables, and methods
- **Intelligent relevance scoring** for context selection
- **Large codebase support** (Unreal Engine-scale projects)
- **`.morpheusignore` support** to exclude sensitive files

### 🤖 Agent Mode
- **Autonomous multi-step task execution** for development workflows
- **Checkpoint system** for safe rollbacks
- **Confirmation steps** for code modifications
- **Task planning** with AI-powered step breakdown
- **Command execution** via Node.js child_process

### 🔒 Security Features
- **Secure API key storage** using VS Code's SecretStorage API
- **Content sanitization** to remove sensitive data
- **File safety validation** before processing
- **Configurable ignore patterns** via `.morpheusignore`

### 💰 Business Cost Reduction
- **Response caching** to minimize API costs
- **Local model fallback** support (future enhancement)
- **Usage tracking** and cost estimation
- **Automated repetitive tasks** (linting, testing, report generation)

## Installation

1. Clone this repository
2. Install dependencies: `npm install`
3. Compile the extension: `npm run compile`
4. Press `F5` to launch a new VS Code window with the extension loaded

## Configuration

### API Key Setup
1. Open Command Palette (`Cmd/Ctrl+Shift+P`)
2. Run "Morpheus: Configure Settings"
3. Enter your xAI Grok API key when prompted

### Settings
```json
{
  "morpheus.enableInlineCompletions": true,
  "morpheus.enableContextEngine": true,
  "morpheus.maxContextTokens": 200000,
  "morpheus.cacheResponses": true,
  "morpheus.outputDirectory": "./morpheus-output",
  "morpheus.supportedLanguages": ["python", "vue", "typescript", "javascript"],
  "morpheus.model": "grok-3-latest"
}
```

## Usage

### Opening the Chat Panel
- Command Palette: "Morpheus: Open Chat"
- Right-click in editor: "Open Morpheus Chat"
- Activity Bar: Click the Morpheus icon

### Using Agent Mode
1. Command Palette: "Morpheus: Toggle Agent Mode"
2. Status bar will show "🤖 Agent: ON"
3. Use chat to give complex development tasks
4. Agent will break down tasks and execute them step-by-step

### Code Completions
- Simply start typing in supported files
- Completions appear automatically
- Press `Tab` or `Cmd/Ctrl+→` to accept

## Commands

| Command | Description |
|---------|-------------|
| `morpheus.openChat` | Open the chat interface |
| `morpheus.toggleAgentMode` | Enable/disable autonomous agent mode |
| `morpheus.refreshContext` | Refresh the context engine index |
| `morpheus.reviewChanges` | Review pending agent changes |
| `morpheus.configureSettings` | Open extension settings |

## File Structure

```
morpheus/
├── src/
│   ├── core/
│   │   ├── contextEngine.ts      # Real-time codebase analysis
│   │   ├── agentMode.ts          # Autonomous task execution
│   │   ├── securityManager.ts    # Security and privacy features
│   │   └── configurationManager.ts # Settings management
│   ├── providers/
│   │   └── completionProvider.ts # Inline code completions
│   ├── services/
│   │   └── aiService.ts          # xAI Grok API integration
│   ├── ui/
│   │   └── chatPanel.ts          # Chat interface management
│   └── extension.ts              # Main extension entry point
├── media/
│   ├── chat.css                  # Chat panel styling
│   └── chat.js                   # Chat panel JavaScript
├── package.json                  # Extension manifest
├── webpack.config.js             # Build configuration
└── .morpheusignore              # Default ignore patterns
```

## Business Use Cases

### Report Generation
```python
# Ask Morpheus: "Generate a Python script to create a CSV report from PostgreSQL data"
import pandas as pd
import psycopg2

def generate_sales_report(connection_string, output_path):
    """Generate sales report from PostgreSQL database"""
    # Generated code with proper error handling
```

### Code Optimization
```typescript
// Ask Morpheus: "Optimize this Vue component for performance"
// Morpheus will suggest improvements like:
// - Computed properties instead of methods
// - v-memo for expensive renders
// - Proper key usage in v-for loops
```

### Test Generation
```python
# Agent Mode task: "Create unit tests for the user authentication module"
# Morpheus will:
# 1. Analyze the authentication code
# 2. Generate comprehensive test cases
# 3. Create test files with proper structure
# 4. Include edge cases and error conditions
```

## Development

### Building
```bash
npm install
npm run compile
```

### Testing
```bash
npm run test
```

### Packaging
```bash
npm run package
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Support

For issues and feature requests, please use the GitHub issue tracker.

## Roadmap

- [ ] Local model integration (Hugging Face CodeBERT)
- [ ] Git integration for better context
- [ ] Multi-language support expansion
- [ ] Advanced refactoring capabilities
- [ ] Team collaboration features
- [ ] Custom business rule templates

---

**Morpheus** - Transforming development productivity through AI automation 🚀
