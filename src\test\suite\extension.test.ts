import * as assert from 'assert';
import * as vscode from 'vscode';
import * as myExtension from '../../extension';

suite('Extension Test Suite', () => {
    vscode.window.showInformationMessage('Start all tests.');

    test('Extension should be present', () => {
        assert.ok(vscode.extensions.getExtension('morpheus-dev.morpheus'));
    });

    test('Extension should activate', async () => {
        const extension = vscode.extensions.getExtension('morpheus-dev.morpheus');
        if (extension) {
            await extension.activate();
            assert.ok(extension.isActive);
        }
    });

    test('Commands should be registered', async () => {
        const commands = await vscode.commands.getCommands(true);
        
        const expectedCommands = [
            'morpheus.openChat',
            'morpheus.toggleAgentMode',
            'morpheus.refreshContext',
            'morpheus.reviewChanges',
            'morpheus.configureSettings'
        ];

        for (const command of expectedCommands) {
            assert.ok(commands.includes(command), `Command ${command} should be registered`);
        }
    });

    test('Configuration should have default values', () => {
        const config = vscode.workspace.getConfiguration('morpheus');
        
        assert.strictEqual(config.get('enableInlineCompletions'), true);
        assert.strictEqual(config.get('enableContextEngine'), true);
        assert.strictEqual(config.get('maxContextTokens'), 200000);
        assert.strictEqual(config.get('cacheResponses'), true);
    });
});
