{"name": "morpheus", "displayName": "Morpheus - AI-Powered Development Assistant", "description": "Advanced AI-powered VS Code extension for code completion, chat assistance, and automated development tasks", "version": "1.0.0", "publisher": "morpheus-dev", "repository": {"type": "git", "url": "https://github.com/morpheus-dev/morpheus-vscode"}, "homepage": "https://github.com/morpheus-dev/morpheus-vscode#readme", "bugs": {"url": "https://github.com/morpheus-dev/morpheus-vscode/issues"}, "license": "MIT", "engines": {"vscode": "^1.92.0"}, "categories": ["Programming Languages", "Machine Learning", "Other"], "keywords": ["ai", "code-completion", "chat", "automation", "python", "vue", "typescript"], "activationEvents": ["onStartupFinished"], "main": "./dist/extension.js", "contributes": {"viewsContainers": {"activitybar": [{"id": "morpheus", "title": "Morpheus", "icon": "$(terminal)"}]}, "views": {"morpheus": [{"id": "morpheus.chatView", "name": "Cha<PERSON>", "type": "webview", "visibility": "visible"}, {"id": "morpheus.agentView", "name": "Agent Mode", "type": "webview", "visibility": "collapsed"}, {"id": "morpheus.workspaceView", "name": "Workspace Index", "type": "webview", "visibility": "collapsed"}]}, "commands": [{"command": "morpheus.openChat", "title": "Open Morpheus Chat", "category": "Morpheus"}, {"command": "morpheus.toggleAgentMode", "title": "Toggle Agent Mode", "category": "Morpheus"}, {"command": "morpheus.refreshIndex", "title": "Refresh Workspace Index", "category": "Morpheus"}, {"command": "morpheus.showIndexStatus", "title": "Show Index Status", "category": "Morpheus"}, {"command": "morpheus.openSidebar", "title": "Open Sidebar", "category": "Morpheus"}, {"command": "morpheus.showActivityBar", "title": "Show in Activity Bar", "category": "Morpheus"}, {"command": "morpheus.refreshContext", "title": "Refresh Context Engine", "category": "Morpheus"}, {"command": "morpheus.reviewChanges", "title": "Review Pending Changes", "category": "Morpheus"}, {"command": "morpheus.configureSettings", "title": "Configure Morpheus Settings", "category": "Morpheus"}], "configuration": {"title": "Morpheus", "properties": {"morpheus.apiKey": {"type": "string", "default": "", "description": "xAI Grok API Key (stored securely)", "scope": "application"}, "morpheus.enableInlineCompletions": {"type": "boolean", "default": true, "description": "Enable inline code completions"}, "morpheus.enableContextEngine": {"type": "boolean", "default": true, "description": "Enable real-time context analysis"}, "morpheus.maxContextTokens": {"type": "number", "default": 200000, "description": "Maximum context window size in tokens"}, "morpheus.cacheResponses": {"type": "boolean", "default": true, "description": "Cache AI responses to reduce costs"}, "morpheus.outputDirectory": {"type": "string", "default": "./morpheus-output", "description": "Directory for generated files and reports"}, "morpheus.excludePatterns": {"type": "array", "items": {"type": "string"}, "default": ["node_modules/**", ".git/**", "*.log", "*.tmp"], "description": "File patterns to exclude from analysis"}, "morpheus.supportedLanguages": {"type": "array", "items": {"type": "string"}, "default": ["python", "vue", "typescript", "javascript"], "description": "Programming languages to analyze"}}}, "menus": {"editor/context": [{"command": "morpheus.openChat", "group": "morpheus", "when": "editorTextFocus"}], "commandPalette": [{"command": "morpheus.openChat"}, {"command": "morpheus.toggleAgentMode"}, {"command": "morpheus.refreshIndex"}, {"command": "morpheus.showIndexStatus"}, {"command": "morpheus.refreshContext"}, {"command": "morpheus.reviewChanges"}, {"command": "morpheus.configureSettings"}]}}, "scripts": {"vscode:prepublish": "npm run package", "compile": "webpack", "watch": "webpack --watch", "package": "webpack --mode production --devtool hidden-source-map", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "npm run compile-tests && npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@types/vscode": "^1.92.0", "@types/node": "20.x", "@types/mocha": "^10.0.7", "@types/glob": "^8.1.0", "@typescript-eslint/eslint-plugin": "^7.14.1", "@typescript-eslint/parser": "^7.11.0", "@vscode/test-electron": "^2.4.0", "eslint": "^8.57.0", "glob": "^10.3.10", "mocha": "^10.6.0", "typescript": "^5.4.5", "webpack": "^5.92.1", "webpack-cli": "^5.1.4", "ts-loader": "^9.5.1"}, "dependencies": {"chokidar": "^3.6.0", "ignore": "^5.3.1", "axios": "^1.7.2", "vue": "^3.4.31"}}