/* Morpheus Chat Panel Styles - Matrix Theme */
:root {
    --vscode-font-family: var(--vscode-font-family);
    --matrix-green: #00ff41;
    --matrix-dark-green: #008f11;
    --matrix-black: #0d1117;
    --matrix-dark-black: #000000;
    --matrix-gray: #1c2128;
    --matrix-light-gray: #30363d;
    --primary-color: var(--matrix-green);
    --secondary-color: var(--matrix-black);
    --text-color: var(--matrix-green);
    --background-color: var(--matrix-black);
    --border-color: var(--matrix-dark-green);
    --input-background: var(--matrix-gray);
    --button-background: var(--matrix-dark-green);
    --button-foreground: var(--matrix-green);
    --error-color: #ff4444;
    --success-color: var(--matrix-green);
    --hover-color: var(--matrix-light-gray);
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Courier New', 'Consolas', monospace;
    background-color: var(--background-color);
    color: var(--text-color);
    height: 100vh;
    overflow: hidden;
    background-image:
        radial-gradient(circle at 20% 50%, rgba(0, 255, 65, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(0, 255, 65, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(0, 255, 65, 0.08) 0%, transparent 50%);
}

#app {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 2px solid var(--border-color);
    background: linear-gradient(135deg, var(--matrix-black) 0%, var(--matrix-gray) 100%);
    box-shadow: 0 2px 8px rgba(0, 255, 65, 0.1);
}

.chat-header h2 {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
    color: var(--matrix-green);
    text-shadow: 0 0 10px rgba(0, 255, 65, 0.5);
    font-family: 'Courier New', monospace;
}

.header-actions {
    display: flex;
    gap: 8px;
}

.header-actions button {
    background: var(--matrix-gray);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    cursor: pointer;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-family: 'Courier New', monospace;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.header-actions button:hover {
    background-color: var(--hover-color);
    border-color: var(--matrix-green);
    box-shadow: 0 0 8px rgba(0, 255, 65, 0.3);
    transform: translateY(-1px);
}

.chat-container {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    display: flex;
    flex-direction: column;
}

.messages {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.message {
    display: flex;
    flex-direction: column;
    max-width: 85%;
    word-wrap: break-word;
}

.message.user {
    align-self: flex-end;
}

.message.assistant {
    align-self: flex-start;
}

.message-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
    font-size: 12px;
    opacity: 0.7;
}

.message-content {
    padding: 12px 16px;
    border-radius: 8px;
    line-height: 1.5;
    position: relative;
    font-family: 'Courier New', monospace;
    border: 1px solid var(--border-color);
}

.message.user .message-content {
    background: transparent;
    color: var(--matrix-green);
    border-bottom-right-radius: 4px;
    border: 1px solid var(--border-color);
    font-weight: 500;
}

.message.assistant .message-content {
    background: linear-gradient(135deg, var(--matrix-gray) 0%, var(--matrix-light-gray) 100%);
    border: 1px solid var(--border-color);
    border-bottom-left-radius: 4px;
    color: var(--matrix-green);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.message-content pre {
    background-color: var(--vscode-textCodeBlock-background);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 12px;
    margin: 8px 0;
    overflow-x: auto;
    font-family: var(--vscode-editor-font-family);
    font-size: var(--vscode-editor-font-size);
}

.message-content code {
    background-color: var(--vscode-textCodeBlock-background);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: var(--vscode-editor-font-family);
    font-size: 0.9em;
}

.message-content pre code {
    background: none;
    padding: 0;
}

.code-block {
    position: relative;
    margin: 8px 0;
}

.code-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--vscode-sideBar-background);
    border: 1px solid var(--border-color);
    border-bottom: none;
    padding: 8px 12px;
    border-radius: 4px 4px 0 0;
    font-size: 12px;
}

.code-actions {
    display: flex;
    gap: 8px;
}

.code-actions button {
    background: none;
    border: none;
    color: var(--text-color);
    cursor: pointer;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    transition: background-color 0.2s;
}

.code-actions button:hover {
    background-color: var(--vscode-toolbar-hoverBackground);
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    font-style: italic;
    opacity: 0.7;
}

.typing-indicator::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--matrix-green);
    animation: matrixPulse 1.5s infinite;
    box-shadow: 0 0 10px var(--matrix-green);
}

@keyframes matrixPulse {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.2);
    }
}

.input-container {
    display: flex;
    gap: 8px;
    padding: 16px;
    border-top: 2px solid var(--border-color);
    background: linear-gradient(135deg, var(--matrix-black) 0%, var(--matrix-gray) 100%);
    box-shadow: 0 -2px 8px rgba(0, 255, 65, 0.1);
}

#messageInput {
    flex: 1;
    background-color: var(--input-background);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 12px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    resize: vertical;
    min-height: 44px;
    max-height: 120px;
    transition: all 0.3s ease;
}

#messageInput:focus {
    outline: none;
    border-color: var(--matrix-green);
    box-shadow: 0 0 0 2px rgba(0, 255, 65, 0.3), 0 0 10px rgba(0, 255, 65, 0.2);
    background-color: var(--matrix-light-gray);
}

#sendBtn {
    background: linear-gradient(135deg, var(--matrix-dark-green) 0%, var(--matrix-green) 100%);
    color: var(--matrix-black);
    border: 1px solid var(--matrix-green);
    border-radius: 6px;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 600;
    font-family: 'Courier New', monospace;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 60px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

#sendBtn:hover {
    background: linear-gradient(135deg, var(--matrix-green) 0%, var(--matrix-dark-green) 100%);
    box-shadow: 0 0 15px rgba(0, 255, 65, 0.4);
    transform: translateY(-1px);
}

#sendBtn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: var(--matrix-gray);
    box-shadow: none;
    transform: none;
}

.error-message {
    background: linear-gradient(135deg, rgba(255, 68, 68, 0.1) 0%, rgba(255, 68, 68, 0.05) 100%);
    color: var(--error-color);
    border: 1px solid var(--error-color);
    border-radius: 6px;
    padding: 12px;
    margin: 8px 0;
    font-family: 'Courier New', monospace;
}

.success-message {
    background: linear-gradient(135deg, rgba(0, 255, 65, 0.2) 0%, rgba(0, 255, 65, 0.1) 100%);
    color: var(--matrix-green);
    border: 1px solid var(--matrix-green);
    border-radius: 6px;
    padding: 8px 12px;
    margin: 4px 0;
    font-size: 12px;
    font-family: 'Courier New', monospace;
    box-shadow: 0 0 10px rgba(0, 255, 65, 0.2);
}

.processing-steps {
    background: linear-gradient(135deg, var(--matrix-gray) 0%, var(--matrix-light-gray) 100%);
    border: 1px solid var(--matrix-dark-green);
    border-radius: 8px;
    padding: 12px;
    margin: 8px 0;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    max-height: 200px;
    overflow-y: auto;
}

.processing-step {
    color: var(--matrix-green);
    padding: 4px 0;
    border-bottom: 1px solid rgba(0, 255, 65, 0.1);
    animation: fadeInStep 0.3s ease-in;
}

.processing-step:last-child {
    border-bottom: none;
}

@keyframes fadeInStep {
    from {
        opacity: 0;
        transform: translateX(-10px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.context-info {
    background-color: var(--vscode-sideBar-background);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 12px;
    margin: 8px 0;
    font-size: 12px;
}

.context-info h4 {
    margin-bottom: 8px;
    color: var(--primary-color);
}

.context-info ul {
    list-style: none;
    padding-left: 0;
}

.context-info li {
    padding: 2px 0;
    display: flex;
    justify-content: space-between;
}

.symbol-type {
    opacity: 0.7;
    font-size: 11px;
}

/* Scrollbar styling */
.chat-container::-webkit-scrollbar {
    width: 8px;
}

.chat-container::-webkit-scrollbar-track {
    background: var(--vscode-scrollbarSlider-background);
}

.chat-container::-webkit-scrollbar-thumb {
    background: var(--vscode-scrollbarSlider-background);
    border-radius: 4px;
}

.chat-container::-webkit-scrollbar-thumb:hover {
    background: var(--vscode-scrollbarSlider-hoverBackground);
}

/* Responsive design */
@media (max-width: 600px) {
    .message {
        max-width: 95%;
    }
    
    .input-container {
        flex-direction: column;
    }
    
    #sendBtn {
        align-self: flex-end;
        min-width: 80px;
    }
}

/* Animation for new messages */
.message {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
