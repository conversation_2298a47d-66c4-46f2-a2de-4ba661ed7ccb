import * as vscode from 'vscode';
import * as path from 'path';
import { ContextEngine } from '../core/contextEngine';
import { SecurityManager } from '../core/securityManager';
import { AIService, ChatMessage } from '../services/aiService';
import { AgentMode } from '../core/agentMode';

export class ChatPanel {
    public static currentPanel: ChatPanel | undefined;
    private readonly panel: vscode.WebviewPanel;
    private readonly extensionUri: vscode.Uri;
    private disposables: vscode.Disposable[] = [];
    private contextEngine: ContextEngine;
    private securityManager: SecurityManager;
    private aiService: AIService;
    private agentMode: AgentMode;
    private chatHistory: ChatMessage[] = [];
    private context: vscode.ExtensionContext;

    constructor(context: vscode.ExtensionContext, contextEngine: ContextEngine, securityManager: SecurityManager, agentMode: AgentMode) {
        this.context = context;
        this.contextEngine = contextEngine;
        this.securityManager = securityManager;
        this.agentMode = agentMode;
        this.aiService = new AIService();
        this.extensionUri = context.extensionUri;

        const column = vscode.window.activeTextEditor
            ? vscode.ViewColumn.Beside
            : undefined;

        this.panel = vscode.window.createWebviewPanel(
            'morpheusChat',
            'Morpheus Chat',
            column || vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: true,
                localResourceRoots: [
                    vscode.Uri.joinPath(context.extensionUri, 'media'),
                    vscode.Uri.joinPath(context.extensionUri, 'out', 'compiled')
                ]
            }
        );

        this.update();
        this.panel.onDidDispose(() => this.dispose(), null, this.disposables);

        this.panel.webview.onDidReceiveMessage(
            async (data) => {
                switch (data.type) {
                    case 'sendMessage':
                        await this.handleUserMessage(data.message);
                        break;
                    case 'clearChat':
                        this.clearChat();
                        break;
                    case 'exportChat':
                        await this.exportChat();
                        break;
                    case 'getContext':
                        await this.sendContextToWebview();
                        break;
                    case 'executeCode':
                        await this.executeCode(data.code, data.language);
                        break;
                    case 'insertCode':
                        await this.insertCodeIntoEditor(data.code);
                        break;
                }
            },
            null,
            this.disposables
        );
    }

    public static createOrShow(context: vscode.ExtensionContext, contextEngine: ContextEngine, securityManager: SecurityManager, agentMode: AgentMode) {
        if (ChatPanel.currentPanel) {
            ChatPanel.currentPanel.panel.reveal();
            return ChatPanel.currentPanel;
        }

        ChatPanel.currentPanel = new ChatPanel(context, contextEngine, securityManager, agentMode);
        return ChatPanel.currentPanel;
    }

    public static kill() {
        ChatPanel.currentPanel?.dispose();
        ChatPanel.currentPanel = undefined;
    }

    public static revive(panel: vscode.WebviewPanel, context: vscode.ExtensionContext, contextEngine: ContextEngine, securityManager: SecurityManager, agentMode: AgentMode) {
        // For revival, we would need to recreate the panel properly
        // For now, just create a new one
        ChatPanel.currentPanel = new ChatPanel(context, contextEngine, securityManager, agentMode);
    }

    public show() {
        this.panel.reveal(vscode.ViewColumn.Beside);
    }

    private async handleUserMessage(message: string) {
        console.log(`🚀🚀🚀 MORPHEUS CHAT: handleUserMessage called with: "${message}"`);

        // Add user message to history
        this.chatHistory.push({ role: 'user', content: message });

        // Send user message to webview immediately
        this.panel.webview.postMessage({
            type: 'userMessage',
            message: message,
            timestamp: new Date().toISOString()
        });

        // Show typing indicator
        this.panel.webview.postMessage({
            type: 'typingStart'
        });

        try {
            // If Agent Mode is enabled, treat ALL messages as tasks
            const agentEnabled = this.agentMode && (this.agentMode as any).isEnabled;
            console.log(`🚀 AGENT CHECK: agentEnabled=${agentEnabled}, message="${message}"`);

            if (agentEnabled) {
                console.log(`🚀 ROUTING TO AGENT MODE (Agent Mode ON - treating all messages as tasks)`);
                await this.handleAgentTask(message);
                return;
            }
            console.log(`🚀 ROUTING TO NORMAL CHAT (Agent Mode OFF)`);

            // Get comprehensive workspace context
            const contextInfo = await this.buildComprehensiveContext(message);

            // Prepare system message with context
            const systemMessage: ChatMessage = {
                role: 'system',
                content: `You are Morpheus, an AI-powered development assistant with full access to the user's workspace and codebase.

🔍 IMPORTANT: You can see all the user's code and workspace context below. When they ask about their code, analyze what you can see directly. DO NOT ask them to paste code - you already have complete access to their workspace.

${contextInfo}

You help with:
1. Code explanation and debugging (analyze the code you can see above)
2. Generating Python/Vue scripts for business tasks
3. Code refactoring and optimization
4. Creating unit tests
5. Generating reports and data processing scripts

Guidelines:
- Analyze code directly from the workspace context provided above
- Reference specific files, functions, and code snippets you can see
- Provide detailed explanations based on the actual code structure
- Point out specific issues with file/line references when possible
- Use the workspace structure and project type for contextually appropriate advice
- The user expects you to understand their codebase without them explaining it
- Provide practical, actionable solutions based on what you can see`
            };

            // Prepare messages for AI
            const messages: ChatMessage[] = [
                systemMessage,
                ...this.chatHistory.slice(-10) // Keep last 10 messages for context
            ];

            // Generate AI response with step visibility
            const response = await this.aiService.generateChatResponse(messages, {
                maxTokens: 2000,
                temperature: 0.7,
                showSteps: true,
                onStep: (step: string) => {
                    // Send step updates to the webview
                    this.panel.webview.postMessage({
                        type: 'processingStep',
                        step: step,
                        timestamp: new Date().toISOString()
                    });
                }
            });

            if (response) {
                // Add AI response to history
                this.chatHistory.push({ role: 'assistant', content: response });
                
                // Send AI response to webview
                this.panel.webview.postMessage({
                    type: 'aiResponse',
                    message: response,
                    timestamp: new Date().toISOString()
                });
            } else {
                throw new Error('Failed to generate AI response');
            }
        } catch (error) {
            console.error('Error handling user message:', error);
            this.panel.webview.postMessage({
                type: 'error',
                message: 'Sorry, I encountered an error processing your message. Please try again.'
            });
        } finally {
            // Hide typing indicator
            this.panel.webview.postMessage({
                type: 'typingEnd'
            });
        }
    }

    private async buildComprehensiveContext(userMessage: string): Promise<string> {
        console.log(`🚀 MORPHEUS DEBUG: buildComprehensiveContext called with message: "${userMessage}"`);
        let contextInfo = '';

        // 1. Workspace Overview
        const workspaceStructure = this.contextEngine.getWorkspaceStructure();
        const indexedFileCount = this.contextEngine.getIndexedFileCount();
        const allSymbols = this.contextEngine.getAllSymbols();

        contextInfo += `=== WORKSPACE CONTEXT ===\n`;
        contextInfo += `Project Type: ${workspaceStructure?.moduleType || 'unknown'}\n`;
        contextInfo += `Files Indexed: ${indexedFileCount}\n`;
        contextInfo += `Total Symbols: ${allSymbols.length}\n`;

        if (vscode.workspace.workspaceFolders?.[0]) {
            contextInfo += `Workspace: ${vscode.workspace.workspaceFolders[0].uri.fsPath}\n`;
        }

        // 2. Current File Context (if any)
        const activeEditor = vscode.window.activeTextEditor;
        if (activeEditor) {
            const document = activeEditor.document;
            const selection = activeEditor.selection;

            contextInfo += `\n=== CURRENT FILE ===\n`;
            contextInfo += `File: ${document.fileName}\n`;
            contextInfo += `Language: ${document.languageId}\n`;

            // Always include the full current file content
            const fileContent = document.getText();

            // Include selected text if any
            if (!selection.isEmpty) {
                const selectedText = document.getText(selection);
                contextInfo += `\nSelected Code:\n\`\`\`${document.languageId}\n${selectedText}\n\`\`\`\n`;

                // Also include full file content for context
                if (fileContent.length > 0) {
                    contextInfo += `\nFull File Content:\n\`\`\`${document.languageId}\n${fileContent.substring(0, 8000)}${fileContent.length > 8000 ? '\n... (truncated)' : ''}\n\`\`\`\n`;
                }
            } else {
                // Include current file content (larger limit for better analysis)
                if (fileContent.length > 0) {
                    contextInfo += `\nFile Content:\n\`\`\`${document.languageId}\n${fileContent.substring(0, 8000)}${fileContent.length > 8000 ? '\n... (truncated)' : ''}\n\`\`\`\n`;
                }
            }

            // Get symbols from current file for additional context
            const currentFileSymbols = this.contextEngine.getSymbolsInFile(document.uri.fsPath);
            if (currentFileSymbols && currentFileSymbols.length > 0) {
                contextInfo += `\nSymbols in this file:\n`;
                currentFileSymbols.forEach(symbol => {
                    contextInfo += `- ${symbol.name} (${symbol.type})${symbol.docstring ? ': ' + symbol.docstring.substring(0, 100) : ''}\n`;
                });
            }
        }

        // 3. Task-Relevant Context (prioritize files based on user message)
        console.log(`🔍 Getting task-relevant files for message: "${userMessage}"`);
        const taskRelevantFiles = this.getTaskRelevantFiles(userMessage);
        console.log(`🔍 Found ${taskRelevantFiles.length} task-relevant files:`, taskRelevantFiles.map(f => `${f.filePath} (${f.score.toFixed(2)})`));

        if (taskRelevantFiles.length > 0) {
            contextInfo += `\n=== RELEVANT FILES FOR YOUR REQUEST ===\n`;
            taskRelevantFiles.forEach(fileInfo => {
                contextInfo += `\nFile: ${fileInfo.filePath} (relevance: ${fileInfo.score.toFixed(2)})\n`;
                contextInfo += `Language: ${fileInfo.language}\n`;
                contextInfo += `Content:\n\`\`\`${fileInfo.language}\n${fileInfo.content.substring(0, 3000)}${fileInfo.content.length > 3000 ? '\n... (truncated)' : ''}\n\`\`\`\n`;
            });
        } else {
            // Fallback: get files related to currently open file or general workspace
            const currentFile = activeEditor?.document.uri.fsPath;
            if (currentFile) {
                const relatedFiles = this.getRelatedFilesWithContent(currentFile);
                if (relatedFiles.length > 0) {
                    contextInfo += `\n=== RELATED FILES ===\n`;
                    relatedFiles.forEach(fileInfo => {
                        contextInfo += `\nFile: ${fileInfo.filePath}\n`;
                        contextInfo += `Language: ${fileInfo.language}\n`;
                        contextInfo += `Content:\n\`\`\`${fileInfo.language}\n${fileInfo.content.substring(0, 2000)}${fileInfo.content.length > 2000 ? '\n... (truncated)' : ''}\n\`\`\`\n`;
                    });
                }
            } else {
                // If no current file, get general workspace context with actual file contents
                const workspaceFiles = Array.from(this.contextEngine.getAllSymbols())
                    .reduce((acc, symbol) => {
                        if (!acc.includes(symbol.filePath)) {
                            acc.push(symbol.filePath);
                        }
                        return acc;
                    }, [] as string[])
                    .slice(0, 3); // Top 3 files to avoid overwhelming context

                if (workspaceFiles.length > 0) {
                    contextInfo += `\n=== WORKSPACE FILES WITH CONTENT ===\n`;
                    for (const filePath of workspaceFiles) {
                        const fileIndex = this.contextEngine.getFileIndex(filePath);
                        if (fileIndex) {
                            contextInfo += `\nFile: ${filePath}\n`;
                            contextInfo += `Language: ${fileIndex.language}\n`;
                            contextInfo += `Content:\n\`\`\`${fileIndex.language}\n${fileIndex.content.substring(0, 3000)}${fileIndex.content.length > 3000 ? '\n... (truncated)' : ''}\n\`\`\`\n`;
                        }
                    }
                }
            }
        }

        // 4. Context based on user message
        if (this.isCodeAnalysisRequest(userMessage)) {
            contextInfo += `\n=== ANALYSIS INSTRUCTION ===\n`;
            contextInfo += `The user is asking for code analysis. All relevant code context is provided above.\n`;
            contextInfo += `Please analyze the code directly without asking the user to paste it.\n`;
        }

        return contextInfo;
    }

    private getRelatedFilesWithContent(currentFile: string): Array<{filePath: string, language: string, content: string}> {
        const relatedFiles: Array<{filePath: string, language: string, content: string}> = [];
        const allSymbols = this.contextEngine.getAllSymbols();

        // Get files that are related to the current file (same directory, imports, etc.)
        const currentDir = require('path').dirname(currentFile);
        const relatedFilePaths = allSymbols
            .filter(symbol => {
                const symbolDir = require('path').dirname(symbol.filePath);
                return symbolDir === currentDir && symbol.filePath !== currentFile;
            })
            .map(symbol => symbol.filePath)
            .filter((path, index, arr) => arr.indexOf(path) === index) // Remove duplicates
            .slice(0, 3); // Limit to 3 related files

        for (const filePath of relatedFilePaths) {
            const fileIndex = this.contextEngine.getFileIndex(filePath);
            if (fileIndex) {
                relatedFiles.push({
                    filePath: fileIndex.filePath,
                    language: fileIndex.language,
                    content: fileIndex.content
                });
            }
        }

        return relatedFiles;
    }

    private isCodeAnalysisRequest(message: string): boolean {
        const analysisKeywords = [
            'wrong', 'error', 'bug', 'issue', 'problem', 'fix', 'debug',
            'analyze', 'review', 'check', 'improve', 'optimize', 'refactor',
            'what does', 'how does', 'explain', 'understand', 'what is'
        ];

        const lowerMessage = message.toLowerCase();
        return analysisKeywords.some(keyword => lowerMessage.includes(keyword));
    }

    private isAgentTask(message: string): boolean {
        // Check if the message looks like a task that should be executed by the agent
        const taskKeywords = [
            'create', 'generate', 'write', 'build', 'make', 'run', 'execute',
            'script', 'file', 'function', 'class', 'test', 'report'
        ];

        const messageLower = message.toLowerCase();
        const hasTaskKeyword = taskKeywords.some(keyword => messageLower.includes(keyword));
        const hasContext = messageLower.includes('python') || messageLower.includes('file') ||
                          messageLower.includes('script') || messageLower.includes('run');

        console.log(`🚀 isAgentTask: "${message}" -> hasTaskKeyword=${hasTaskKeyword}, hasContext=${hasContext}`);

        return hasTaskKeyword && hasContext;
    }

    private async handleAgentTask(message: string) {
        try {
            // Send agent status message
            this.panel.webview.postMessage({
                type: 'aiResponse',
                message: `🤖 **Agent Mode Activated**\n\nI'll execute this task for you: "${message}"\n\nLet me break this down into steps...`,
                timestamp: new Date().toISOString()
            });

            // Execute the task using agent mode
            const task = await this.agentMode.executeTask(message);

            // Send appropriate message based on actual task status
            if (task.status === 'completed') {
                this.panel.webview.postMessage({
                    type: 'aiResponse',
                    message: `✅ **Task Completed Successfully**\n\nTask: ${task.description}\nSteps executed: ${task.steps.length}`,
                    timestamp: new Date().toISOString()
                });

                // Add to chat history
                this.chatHistory.push({
                    role: 'assistant',
                    content: `Agent successfully executed task: ${message}`
                });
            } else {
                // Task failed or was cancelled
                const failureMessage = `❌ **Task ${task.status.charAt(0).toUpperCase() + task.status.slice(1)}**\n\nTask: ${task.description}\nError: ${task.error || 'Unknown error'}\nSteps attempted: ${task.steps.length}`;

                this.panel.webview.postMessage({
                    type: 'aiResponse',
                    message: failureMessage,
                    timestamp: new Date().toISOString()
                });

                // Add to chat history
                this.chatHistory.push({
                    role: 'assistant',
                    content: `Agent task ${task.status}: ${task.error || 'Unknown error'}`
                });
            }

        } catch (error) {
            const errorMessage = `❌ **Agent Task Failed**\n\nError: ${error instanceof Error ? error.message : String(error)}`;

            this.panel.webview.postMessage({
                type: 'aiResponse',
                message: errorMessage,
                timestamp: new Date().toISOString()
            });

            this.chatHistory.push({
                role: 'assistant',
                content: errorMessage
            });
        }
    }

    private clearChat() {
        this.chatHistory = [];
        this.panel.webview.postMessage({
            type: 'clearChat'
        });
    }

    private async exportChat() {
        if (this.chatHistory.length === 0) {
            vscode.window.showInformationMessage('No chat history to export');
            return;
        }

        const chatExport = {
            timestamp: new Date().toISOString(),
            messages: this.chatHistory
        };

        const exportContent = JSON.stringify(chatExport, null, 2);
        
        try {
            const uri = await vscode.window.showSaveDialog({
                defaultUri: vscode.Uri.file(`morpheus-chat-${Date.now()}.json`),
                filters: {
                    'JSON files': ['json'],
                    'All files': ['*']
                }
            });

            if (uri) {
                await vscode.workspace.fs.writeFile(uri, Buffer.from(exportContent, 'utf8'));
                vscode.window.showInformationMessage('Chat exported successfully');
            }
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to export chat: ${error}`);
        }
    }

    private async sendContextToWebview() {
        const activeEditor = vscode.window.activeTextEditor;
        if (!activeEditor) {
            return;
        }

        const document = activeEditor.document;
        const symbols = this.contextEngine.getSymbolsInFile(document.uri.fsPath);
        
        this.panel.webview.postMessage({
            type: 'contextInfo',
            data: {
                fileName: document.fileName,
                language: document.languageId,
                symbols: symbols.map(s => ({
                    name: s.name,
                    type: s.type,
                    line: s.startLine
                }))
            }
        });
    }

    private async executeCode(code: string, language: string) {
        const terminal = vscode.window.createTerminal('Morpheus Code Execution');
        
        try {
            // Create a temporary file
            const tempFile = this.securityManager.createSecureTempFile(code, this.getFileExtension(language));
            
            // Execute based on language
            switch (language.toLowerCase()) {
                case 'python':
                    terminal.sendText(`python "${tempFile}"`);
                    break;
                case 'javascript':
                case 'typescript':
                    terminal.sendText(`node "${tempFile}"`);
                    break;
                default:
                    vscode.window.showWarningMessage(`Code execution not supported for ${language}`);
                    return;
            }
            
            terminal.show();
            
            this.panel.webview.postMessage({
                type: 'codeExecuted',
                message: `Code executed in terminal`
            });
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to execute code: ${error}`);
        }
    }

    private async insertCodeIntoEditor(code: string) {
        const activeEditor = vscode.window.activeTextEditor;
        if (!activeEditor) {
            vscode.window.showWarningMessage('No active editor to insert code into');
            return;
        }

        const selection = activeEditor.selection;
        await activeEditor.edit(editBuilder => {
            if (selection.isEmpty) {
                editBuilder.insert(selection.active, code);
            } else {
                editBuilder.replace(selection, code);
            }
        });

        this.panel.webview.postMessage({
            type: 'codeInserted',
            message: 'Code inserted into editor'
        });
    }

    private getFileExtension(language: string): string {
        const extensions: { [key: string]: string } = {
            'python': '.py',
            'javascript': '.js',
            'typescript': '.ts',
            'vue': '.vue'
        };
        return extensions[language.toLowerCase()] || '.txt';
    }

    public updateConfiguration() {
        // Reload webview when configuration changes
        this.update();
    }

    private update() {
        const webview = this.panel.webview;
        this.panel.webview.html = this.getHtmlForWebview(webview);
    }

    private getHtmlForWebview(webview: vscode.Webview) {
        const scriptUri = webview.asWebviewUri(
            vscode.Uri.joinPath(this.extensionUri, 'media', 'chat.js')
        );
        const styleUri = webview.asWebviewUri(
            vscode.Uri.joinPath(this.extensionUri, 'media', 'chat.css')
        );

        const nonce = this.getNonce();

        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}';">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <link href="${styleUri}" rel="stylesheet">
            <title>Morpheus Chat</title>
        </head>
        <body>
            <div id="app">
                <div class="chat-header">
                    <h2>MORPHEUS ASSISTANT</h2>
                    <div class="header-actions">
                        <button id="clearBtn" title="Clear Chat">CLEAR</button>
                        <button id="exportBtn" title="Export Chat">EXPORT</button>
                        <button id="contextBtn" title="Get Context">CONTEXT</button>
                    </div>
                </div>
                
                <div id="chatContainer" class="chat-container">
                    <div id="messages" class="messages"></div>
                    <div id="typingIndicator" class="typing-indicator" style="display: none;">
                        <span>Morpheus is thinking...</span>
                    </div>
                </div>
                
                <div class="input-container">
                    <textarea id="messageInput" placeholder="Ask Morpheus anything about your code..." rows="3"></textarea>
                    <button id="sendBtn">Send</button>
                </div>
            </div>
            <script nonce="${nonce}" src="${scriptUri}"></script>
        </body>
        </html>`;
    }

    private getTaskRelevantFiles(userMessage: string): Array<{filePath: string, language: string, content: string, score: number}> {
        const lowerMessage = userMessage.toLowerCase();
        const allFiles = this.contextEngine.getAllSymbols();
        console.log(`🔍 Total symbols from context engine: ${allFiles.length}`);
        console.log(`🔍 All file paths:`, allFiles.map(s => s.filePath));

        const relevantFiles: Array<{filePath: string, score: number}> = [];

        // Score files based on user message keywords
        for (const symbol of allFiles) {
            let score = 0;
            const fileName = path.basename(symbol.filePath).toLowerCase();
            console.log(`🔍 Scoring file: ${fileName} for message: "${lowerMessage}"`);

            // Check for direct name matches
            if (lowerMessage.includes('login') && fileName.includes('login')) {
                score += 1.0;
            }
            if (lowerMessage.includes('vue') && fileName.endsWith('.vue')) {
                score += 0.8;
            }
            if (lowerMessage.includes('component') && fileName.includes('component')) {
                score += 0.7;
            }
            if (lowerMessage.includes('auth') && (fileName.includes('auth') || fileName.includes('login'))) {
                score += 0.9;
            }
            if (lowerMessage.includes('app') && fileName.includes('app')) {
                score += 0.6;
            }
            if (lowerMessage.includes('main') && fileName.includes('main')) {
                score += 0.6;
            }
            if (lowerMessage.includes('data') && fileName.includes('data')) {
                score += 0.6;
            }

            // Check for file type relevance
            if (lowerMessage.includes('python') && fileName.endsWith('.py')) {
                score += 0.6;
            }
            if (lowerMessage.includes('flask') && fileName.includes('app')) {
                score += 0.7;
            }
            if (lowerMessage.includes('javascript') && fileName.endsWith('.js')) {
                score += 0.6;
            }

            console.log(`🔍 File ${fileName} scored: ${score}`);
            if (score > 0.1) {
                relevantFiles.push({ filePath: symbol.filePath, score });
                console.log(`🔍 ✅ Added ${fileName} to relevant files with score ${score}`);
            }
        }

        // Sort by relevance and get top files
        relevantFiles.sort((a, b) => b.score - a.score);
        const topFiles = relevantFiles.slice(0, 5); // Top 5 most relevant files

        // Get file content for relevant files
        const filesWithContent: Array<{filePath: string, language: string, content: string, score: number}> = [];
        for (const {filePath, score} of topFiles) {
            const fileIndex = this.contextEngine.getFileIndex(filePath);
            if (fileIndex) {
                filesWithContent.push({
                    filePath,
                    language: fileIndex.language,
                    content: fileIndex.content,
                    score
                });
            }
        }

        return filesWithContent;
    }

    private getNonce() {
        let text = '';
        const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        for (let i = 0; i < 32; i++) {
            text += possible.charAt(Math.floor(Math.random() * possible.length));
        }
        return text;
    }

    public dispose() {
        ChatPanel.currentPanel = undefined;

        this.panel.dispose();

        while (this.disposables.length) {
            const x = this.disposables.pop();
            if (x) {
                x.dispose();
            }
        }
    }
}
