#!/usr/bin/env node

/**
 * Simple script to test the Morpheus extension functionality
 */

const fs = require('fs');
const path = require('path');

console.log('🤖 Morpheus Extension Test Script');
console.log('==================================');

// Check if extension files exist
const requiredFiles = [
    'dist/extension.js',
    'package.json',
    'media/chat.css',
    'media/chat.js',
    '.morpheusignore'
];

console.log('\n📁 Checking required files...');
let allFilesExist = true;

for (const file of requiredFiles) {
    const filePath = path.join(__dirname, '..', file);
    if (fs.existsSync(filePath)) {
        console.log(`✅ ${file}`);
    } else {
        console.log(`❌ ${file} - MISSING`);
        allFilesExist = false;
    }
}

// Check package.json structure
console.log('\n📦 Checking package.json...');
try {
    const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '..', 'package.json'), 'utf8'));
    
    const requiredFields = ['name', 'displayName', 'version', 'engines', 'main', 'contributes'];
    for (const field of requiredFields) {
        if (packageJson[field]) {
            console.log(`✅ ${field}: ${typeof packageJson[field] === 'object' ? 'defined' : packageJson[field]}`);
        } else {
            console.log(`❌ ${field} - MISSING`);
            allFilesExist = false;
        }
    }

    // Check commands
    if (packageJson.contributes && packageJson.contributes.commands) {
        console.log(`✅ Commands: ${packageJson.contributes.commands.length} defined`);
        packageJson.contributes.commands.forEach(cmd => {
            console.log(`   - ${cmd.command}: ${cmd.title}`);
        });
    }

    // Check configuration
    if (packageJson.contributes && packageJson.contributes.configuration) {
        const configProps = Object.keys(packageJson.contributes.configuration.properties || {});
        console.log(`✅ Configuration: ${configProps.length} properties`);
    }

} catch (error) {
    console.log(`❌ Error reading package.json: ${error.message}`);
    allFilesExist = false;
}

// Check sample files
console.log('\n📄 Checking sample files...');
const sampleFiles = [
    'sample/business_report.py',
    'sample/dashboard.vue'
];

for (const file of sampleFiles) {
    const filePath = path.join(__dirname, '..', file);
    if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath);
        console.log(`✅ ${file} (${Math.round(stats.size / 1024)}KB)`);
    } else {
        console.log(`⚠️  ${file} - Optional sample file missing`);
    }
}

// Check TypeScript compilation output
console.log('\n🔧 Checking compilation output...');
const outDir = path.join(__dirname, '..', 'out');
if (fs.existsSync(outDir)) {
    const files = fs.readdirSync(outDir, { recursive: true });
    const jsFiles = files.filter(f => f.endsWith('.js'));
    const dtsFiles = files.filter(f => f.endsWith('.d.ts'));
    console.log(`✅ Compiled files: ${jsFiles.length} JS, ${dtsFiles.length} declaration files`);
} else {
    console.log(`⚠️  Output directory 'out' not found - run 'npm run compile-tests'`);
}

// Final summary
console.log('\n🎯 Summary');
console.log('==========');
if (allFilesExist) {
    console.log('✅ All required files are present');
    console.log('✅ Extension structure looks good');
    console.log('\n🚀 Ready to test! Press F5 in VS Code to launch the extension.');
    console.log('\n📋 Next steps:');
    console.log('   1. Open VS Code in this directory');
    console.log('   2. Press F5 to launch Extension Development Host');
    console.log('   3. Open Command Palette (Ctrl+Shift+P)');
    console.log('   4. Run "Morpheus: Open Chat" to test the chat interface');
    console.log('   5. Try code completions in Python or Vue files');
    console.log('   6. Toggle Agent Mode with "Morpheus: Toggle Agent Mode"');
} else {
    console.log('❌ Some required files are missing');
    console.log('   Run "npm run compile" to build the extension');
}

console.log('\n📚 Documentation: See README.md for detailed usage instructions');
console.log('🐛 Issues: Check the VS Code Developer Console for any errors');
