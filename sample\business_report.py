"""
Sample Python file for testing Morpheus extension features
Business report generation example
"""

import pandas as pd
import psycopg2
from datetime import datetime, timedelta
import csv
import json

class BusinessReportGenerator:
    """Generate various business reports from database data"""
    
    def __init__(self, connection_string):
        self.connection_string = connection_string
        self.connection = None
    
    def connect_to_database(self):
        """Establish database connection"""
        try:
            self.connection = psycopg2.connect(self.connection_string)
            return True
        except Exception as e:
            print(f"Database connection failed: {e}")
            return False
    
    def generate_sales_report(self, start_date, end_date, output_format='csv'):
        """Generate sales report for specified date range"""
        if not self.connection:
            if not self.connect_to_database():
                return None
        
        query = """
        SELECT 
            DATE(order_date) as date,
            product_name,
            SUM(quantity) as total_quantity,
            SUM(price * quantity) as total_revenue
        FROM orders o
        JOIN products p ON o.product_id = p.id
        WHERE order_date BETWEEN %s AND %s
        GROUP BY DATE(order_date), product_name
        ORDER BY date DESC, total_revenue DESC
        """
        
        try:
            df = pd.read_sql_query(query, self.connection, params=[start_date, end_date])
            
            if output_format == 'csv':
                return self.export_to_csv(df, 'sales_report.csv')
            elif output_format == 'json':
                return self.export_to_json(df, 'sales_report.json')
            else:
                return df
                
        except Exception as e:
            print(f"Error generating sales report: {e}")
            return None
    
    def generate_customer_analysis(self):
        """Analyze customer behavior and purchasing patterns"""
        # This function would benefit from Morpheus code completion
        pass
    
    def process_financial_data(self, data_source):
        """Process financial data from various sources"""
        # Morpheus can help complete this business logic
        pass
    
    def export_to_csv(self, dataframe, filename):
        """Export dataframe to CSV file"""
        try:
            dataframe.to_csv(filename, index=False)
            return f"Report exported to {filename}"
        except Exception as e:
            return f"Export failed: {e}"
    
    def export_to_json(self, dataframe, filename):
        """Export dataframe to JSON file"""
        try:
            dataframe.to_json(filename, orient='records', indent=2)
            return f"Report exported to {filename}"
        except Exception as e:
            return f"Export failed: {e}"

def main():
    """Main function to demonstrate report generation"""
    # Connection string would be configured securely
    conn_string = "postgresql://user:password@localhost:5432/business_db"
    
    generator = BusinessReportGenerator(conn_string)
    
    # Generate report for last 30 days
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    
    result = generator.generate_sales_report(start_date, end_date)
    print(result)

if __name__ == "__main__":
    main()
