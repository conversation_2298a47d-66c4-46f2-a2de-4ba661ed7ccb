// Test file for snippet modification feature
// This file will be used to test the new intelligent snippet modification

class Calculator {
    constructor() {
        this.result = 0;
    }

    add(a, b) {
        return a + b;
    }

    subtract(a, b) {
        return a - b;
    }

    // More methods will be added here by snippet modification
}

function main() {
    const calc = new Calculator();
    console.log('Calculator created');
    
    // Test basic operations
    console.log('2 + 3 =', calc.add(2, 3));
    console.log('5 - 2 =', calc.subtract(5, 2));
}

main();
