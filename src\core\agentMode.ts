import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { spawn, ChildProcess } from 'child_process';
import { ContextEngine } from './contextEngine';
import { SecurityManager } from './securityManager';
import { AIService, ChatMessage } from '../services/aiService';

export interface AgentTask {
    id: string;
    description: string;
    status: 'pending' | 'in-progress' | 'completed' | 'failed';
    steps: AgentStep[];
    result?: string;
    error?: string;
}

export interface AgentStep {
    id: string;
    description: string;
    action: 'create-file' | 'modify-file' | 'run-command' | 'analyze-code' | 'generate-test';
    parameters: any;
    status: 'pending' | 'in-progress' | 'completed' | 'failed';
    result?: string;
    checkpoint?: string; // For rollback
}

export interface SnippetModification {
    modificationType: 'insert' | 'replace' | 'delete';
    targetLocation: {
        startLine: number;
        endLine: number;
        description: string;
    };
    newContent: string;
    reasoning: string;
}

export class AgentMode {
    private context: vscode.ExtensionContext;
    private contextEngine: ContextEngine;
    private securityManager: SecurityManager;
    private aiService: AIService;
    private isEnabled = false;
    private currentTask: AgentTask | null = null;
    private taskHistory: AgentTask[] = [];
    private statusBarItem: vscode.StatusBarItem;
    private contentCache: Map<string, { content: string; timestamp: number }> = new Map();
    private readonly cacheExpirationMs = 10 * 60 * 1000; // 10 minutes
    private progressCallback?: (step: string) => void;

    constructor(context: vscode.ExtensionContext, contextEngine: ContextEngine, securityManager: SecurityManager) {
        this.context = context;
        this.contextEngine = contextEngine;
        this.securityManager = securityManager;
        this.aiService = new AIService();

        // Create status bar item
        this.statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 100);
        this.statusBarItem.command = 'morpheus.toggleAgentMode';
        this.updateStatusBar();
        context.subscriptions.push(this.statusBarItem);
    }

    public toggle(): void {
        this.isEnabled = !this.isEnabled;
        this.updateStatusBar();
        
        if (this.isEnabled) {
            vscode.window.showInformationMessage('Morpheus Agent Mode enabled');
            this.showAgentPanel();
        } else {
            vscode.window.showInformationMessage('Morpheus Agent Mode disabled');
            if (this.currentTask) {
                this.pauseCurrentTask();
            }
        }
    }

    public get enabled(): boolean {
        return this.isEnabled;
    }

    public setProgressCallback(callback: (step: string) => void): void {
        this.progressCallback = callback;
    }

    private reportProgress(step: string): void {
        console.log(`🔄 AGENT PROGRESS: ${step}`);
        if (this.progressCallback) {
            this.progressCallback(step);
        }
    }

    private updateStatusBar(): void {
        if (this.isEnabled) {
            this.statusBarItem.text = '🤖 Agent: ON';
            this.statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.prominentBackground');
        } else {
            this.statusBarItem.text = '🤖 Agent: OFF';
            this.statusBarItem.backgroundColor = undefined;
        }
        this.statusBarItem.show();
    }

    public async executeTask(description: string): Promise<AgentTask> {
        if (!this.isEnabled) {
            throw new Error('Agent mode is not enabled');
        }

        this.reportProgress('🚀 Initializing Morpheus Agent...');

        const task: AgentTask = {
            id: this.generateTaskId(),
            description,
            status: 'pending',
            steps: []
        };

        this.currentTask = task;
        this.taskHistory.push(task);
        this.reportProgress('📋 Task created, analyzing requirements...');

        try {
            // Plan the task
            this.reportProgress('🧠 Planning task execution...');
            await this.planTask(task);

            // Execute the task steps
            this.reportProgress('⚡ Executing task steps...');
            await this.executeTaskSteps(task);

            task.status = 'completed';
            this.reportProgress('✅ Task completed successfully!');
            vscode.window.showInformationMessage(`Task completed: ${description}`);
        } catch (error) {
            task.status = 'failed';
            task.error = error instanceof Error ? error.message : String(error);
            this.reportProgress(`❌ Task failed: ${task.error}`);
            console.error('🚨 AGENT TASK FAILED:', task.error);
            console.error('🚨 Full error details:', error);
            vscode.window.showErrorMessage(`Task failed: ${task.error}`);
        } finally {
            this.currentTask = null;
        }

        return task;
    }

    private async planTask(task: AgentTask): Promise<void> {
        try {
            console.log('Planning task:', task.description);
            this.reportProgress('🔍 Analyzing workspace context...');

            // Get workspace context for better planning
            const workspaceContext = await this.buildWorkspaceContext(task.description);
            this.reportProgress('🤖 Generating execution plan with AI...');

            const response = await this.aiService.generateChatResponse([
                {
                    role: 'system',
                    content: `You are an AI agent that plans development tasks. You have full access to the user's workspace and codebase.

${workspaceContext}

Break down the given task into specific, actionable steps based on the actual files and structure you can see above. Always respond with a simple list of steps, not JSON.`
                },
                {
                    role: 'user',
                    content: `Task: ${task.description}

Please break this down into simple, actionable steps. Use VERY SIMPLE language and be specific about file names and actions.

IMPORTANT: Use these exact action words:
- "Create file" (for new files)
- "Modify file" (for editing existing files)
- "Run command" (for terminal commands)

Example format:
Step 1: Create file "counter.py"
Step 2: Modify file "counter.py" to add counting function
Step 3: Run command "python counter.py"

Use the actual files and structure from the workspace context above.
Create as many steps as needed to complete the task properly - there's no minimum or maximum limit.

Now plan this task: ${task.description}`
                }
            ]);

            console.log('Planning response:', response);

            if (!response) {
                console.error('No response from AI service');
                task.status = 'failed';
                task.error = 'Failed to get response from AI service. Please check your API key and connection.';
                this.reportProgress('❌ Failed to get AI response');
                vscode.window.showErrorMessage('Morpheus: Failed to plan task - no response from AI service. Please check your API configuration.');
                return;
            } else {
                this.reportProgress('📝 Parsing execution plan...');
                task.steps = this.parseTaskPlan(response, task.description);
            }

            console.log('Generated steps:', task.steps);
            this.reportProgress(`✅ Plan ready: ${task.steps.length} steps identified`);
            task.status = 'in-progress';
        } catch (error) {
            console.error('Error in planTask:', error);
            task.status = 'failed';
            task.error = error instanceof Error ? error.message : String(error);
            vscode.window.showErrorMessage(`Morpheus: Failed to plan task - ${task.error}`);
            return;
        }
    }

    private async buildWorkspaceContext(taskDescription: string): Promise<string> {
        // Force refresh the index to pick up any new files
        console.log('🔄 AGENT: Refreshing workspace index...');
        await this.contextEngine.refreshIndex();

        // Wait a moment for the refresh to complete
        await new Promise(resolve => setTimeout(resolve, 500));

        let contextInfo = '';

        // 1. Workspace Overview
        const workspaceStructure = this.contextEngine.getWorkspaceStructure();
        const indexedFileCount = this.contextEngine.getIndexedFileCount();

        contextInfo += `=== WORKSPACE CONTEXT ===\n`;
        contextInfo += `Project Type: ${workspaceStructure?.moduleType || 'unknown'}\n`;
        contextInfo += `Files Indexed: ${indexedFileCount}\n`;
        contextInfo += `Workspace: ${vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || 'none'}\n`;

        // 2. Task-Relevant Files
        const taskRelevantFiles = this.getTaskRelevantFiles(taskDescription);

        if (taskRelevantFiles.length > 0) {
            contextInfo += `\n=== RELEVANT FILES FOR YOUR TASK ===\n`;
            taskRelevantFiles.forEach(fileInfo => {
                contextInfo += `\nFile: ${fileInfo.filePath} (relevance: ${fileInfo.score.toFixed(2)})\n`;
                contextInfo += `Language: ${fileInfo.language}\n`;
                contextInfo += `Content:\n\`\`\`${fileInfo.language}\n${fileInfo.content.substring(0, 2000)}${fileInfo.content.length > 2000 ? '\n... (truncated)' : ''}\n\`\`\`\n`;
            });
        }

        return contextInfo;
    }

    private getTaskRelevantFiles(taskDescription: string): Array<{filePath: string, language: string, content: string, score: number}> {
        const lowerMessage = taskDescription.toLowerCase();
        const allFiles = this.contextEngine.getAllSymbols();
        console.log(`🔍 AGENT: Total symbols from context engine: ${allFiles.length}`);
        console.log(`🔍 AGENT: All file paths:`, allFiles.map(s => s.filePath));

        const relevantFiles: Array<{filePath: string, score: number}> = [];

        // Get unique file paths to avoid duplicates
        const uniqueFilePaths = [...new Set(allFiles.map(s => s.filePath))];
        console.log(`🔍 AGENT: Unique file paths: ${uniqueFilePaths.length}`, uniqueFilePaths.map(p => require('path').basename(p)));

        // Score files based on task description keywords
        for (const filePath of uniqueFilePaths) {
            let score = 0;
            const fileName = require('path').basename(filePath).toLowerCase();
            console.log(`🔍 AGENT: Scoring file: ${fileName} for task: "${lowerMessage}"`);

            // Check for direct name matches
            if (lowerMessage.includes('login') && fileName.includes('login')) {
                score += 1.0;
            }
            if (lowerMessage.includes('vue') && fileName.endsWith('.vue')) {
                score += 0.8;
            }
            if (lowerMessage.includes('component') && fileName.includes('component')) {
                score += 0.7;
            }
            if (lowerMessage.includes('auth') && (fileName.includes('auth') || fileName.includes('login'))) {
                score += 0.9;
            }
            if (lowerMessage.includes('app') && fileName.includes('app')) {
                score += 0.6;
            }
            if (lowerMessage.includes('main') && fileName.includes('main')) {
                score += 0.6;
            }
            if (lowerMessage.includes('data') && fileName.includes('data')) {
                score += 0.6;
            }

            // Backend/API related scoring
            if ((lowerMessage.includes('backend') || lowerMessage.includes('api') || lowerMessage.includes('endpoint')) && fileName.endsWith('.py')) {
                score += 1.0;
            }
            if ((lowerMessage.includes('server') || lowerMessage.includes('endpoint')) && (fileName.includes('app') || fileName.includes('server'))) {
                score += 0.9;
            }
            if (lowerMessage.includes('route') && fileName.includes('route')) {
                score += 0.8;
            }

            // Check for file type relevance
            if (lowerMessage.includes('python') && fileName.endsWith('.py')) {
                score += 0.6;
            }
            if (lowerMessage.includes('flask') && fileName.includes('app')) {
                score += 0.7;
            }
            if (lowerMessage.includes('javascript') && fileName.endsWith('.js')) {
                score += 0.6;
            }

            console.log(`🔍 AGENT: File ${fileName} scored: ${score}`);
            if (score > 0.1) {
                relevantFiles.push({ filePath, score });
                console.log(`🔍 AGENT: ✅ Added ${fileName} to relevant files with score ${score}`);
            }
        }

        // Sort by relevance and get top files
        relevantFiles.sort((a, b) => b.score - a.score);
        const topFiles = relevantFiles.slice(0, 3); // Top 3 most relevant files

        // Get file content for relevant files
        const filesWithContent: Array<{filePath: string, language: string, content: string, score: number}> = [];
        for (const {filePath, score} of topFiles) {
            const fileIndex = this.contextEngine.getFileIndex(filePath);
            if (fileIndex) {
                filesWithContent.push({
                    filePath,
                    language: fileIndex.language,
                    content: fileIndex.content,
                    score
                });
            }
        }

        return filesWithContent;
    }

    private buildPlanningPrompt(description: string): string {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        let contextInfo = '';

        if (workspaceFolder) {
            // Get broader context that includes relevant files based on task description
            contextInfo = this.getTaskRelevantContext(description, 20000);
        }

        return `Task: ${description}

Current workspace context:
${contextInfo}

Please break this task down into specific steps. Each step should be one of these types:
1. create-file: Create a new file
2. modify-file: Modify an existing file
3. run-command: Execute a command (linting, testing, etc.)
4. analyze-code: Analyze existing code
5. generate-test: Generate unit tests

Format your response as a JSON array of steps:
[
  {
    "description": "Step description",
    "action": "create-file|modify-file|run-command|analyze-code|generate-test",
    "parameters": { /* action-specific parameters */ }
  }
]`;
    }

    private getTaskRelevantContext(description: string, maxTokens: number): string {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            return '';
        }

        const lowerDescription = description.toLowerCase();
        const contexts: string[] = [];
        let tokenCount = 0;

        // Get all indexed files
        const allFiles = this.contextEngine.getAllSymbols();
        const relevantFiles: Array<{filePath: string, score: number}> = [];

        // Score files based on task description keywords
        for (const symbol of allFiles) {
            let score = 0;
            const fileName = path.basename(symbol.filePath).toLowerCase();

            // Check for direct name matches
            if (lowerDescription.includes('login') && fileName.includes('login')) {
                score += 1.0;
            }
            if (lowerDescription.includes('vue') && fileName.endsWith('.vue')) {
                score += 0.8;
            }
            if (lowerDescription.includes('component') && fileName.includes('component')) {
                score += 0.7;
            }
            if (lowerDescription.includes('auth') && (fileName.includes('auth') || fileName.includes('login'))) {
                score += 0.9;
            }

            // Check for file type relevance
            if (lowerDescription.includes('python') && fileName.endsWith('.py')) {
                score += 0.6;
            }
            if (lowerDescription.includes('flask') && fileName.includes('app')) {
                score += 0.7;
            }

            if (score > 0.1) {
                relevantFiles.push({ filePath: symbol.filePath, score });
            }
        }

        // Sort by relevance and get top files
        relevantFiles.sort((a, b) => b.score - a.score);
        const topFiles = relevantFiles.slice(0, 8); // Top 8 most relevant files

        // Add context for relevant files
        for (const {filePath, score} of topFiles) {
            if (tokenCount >= maxTokens) {
                break;
            }

            const fileContext = this.contextEngine.getRelevantContext(filePath, Math.floor(maxTokens / topFiles.length));
            if (fileContext) {
                contexts.push(`// Relevant file (score: ${score.toFixed(2)})\n${fileContext}`);
                tokenCount += fileContext.length / 4;
            }
        }

        // If no specific files found, fall back to general workspace context
        if (contexts.length === 0) {
            return this.contextEngine.getRelevantContext(workspaceFolder.uri.fsPath, maxTokens);
        }

        return contexts.join('\n\n');
    }

    private parseTaskPlan(response: string, taskDescription: string): AgentStep[] {
        try {
            console.log('Parsing task plan from response:', response);

            // Try to extract JSON first
            const jsonMatch = response.match(/\[[\s\S]*\]/);
            if (jsonMatch) {
                try {
                    const stepsData = JSON.parse(jsonMatch[0]);
                    return stepsData.map((stepData: any, index: number) => ({
                        id: `step_${index + 1}`,
                        description: stepData.description,
                        action: stepData.action,
                        parameters: stepData.parameters || {},
                        status: 'pending'
                    }));
                } catch (jsonError) {
                    console.log('JSON parsing failed, trying text parsing');
                }
            }

            // Parse as text steps - focus on main steps only
            const steps: AgentStep[] = [];
            const lines = response.split('\n');
            let stepCount = 1;

            for (const line of lines) {
                const trimmed = line.trim();
                // Only process main steps (Step 1:, Step 2:, etc.) - ignore sub-bullets
                if (trimmed.match(/^step\s*\d+:/i)) {
                    const action = this.inferActionFromDescription(trimmed);
                    const parameters = this.inferParametersFromDescription(trimmed, taskDescription);

                    console.log(`Step ${stepCount}: "${trimmed}"`);
                    console.log(`  Action: ${action}`);
                    console.log(`  Initial parameters:`, parameters);

                    // If no parameters were inferred, this is an error
                    if (Object.keys(parameters).length === 0) {
                        throw new Error(`Unable to determine parameters for step: "${trimmed}". The AI response may be too vague or unclear.`);
                    }

                    steps.push({
                        id: `step_${stepCount}`,
                        description: trimmed.replace(/^step\s*\d+:?\s*/i, ''),
                        action,
                        parameters,
                        status: 'pending'
                    });
                    stepCount++;
                }
            }

            if (steps.length === 0) {
                throw new Error('AI response did not contain any valid steps. The response may be malformed.');
            }
            return steps;
        } catch (error) {
            console.error('Failed to parse task plan:', error);
            throw new Error(`Failed to parse AI response: ${error instanceof Error ? error.message : String(error)}`);
        }
    }



    private inferActionFromDescription(description: string): AgentStep['action'] {
        const lower = description.toLowerCase();
        console.log(`Inferring action for: "${description}"`);
        console.log(`  Lowercase: "${lower}"`);

        // Running/executing commands (check this FIRST before other conditions)
        if (lower.includes('run command') || lower.includes('execute command') ||
            (lower.includes('run') && (lower.includes('node') || lower.includes('python') || lower.includes('npm') || lower.includes('yarn')))) {
            console.log(`  → run-command (contains run/execute command)`);
            return 'run-command';
        }

        // File modification (check BEFORE create to avoid "modify file" being classified as create)
        if (lower.includes('modify file') || lower.includes('update file') || lower.includes('change file') ||
            lower.includes('edit file') || lower.includes('add to file')) {
            console.log(`  → modify-file (contains modify/update/change/edit file)`);
            return 'modify-file';
        }

        // Test the application/connection (should be run-command, not generate-test)
        if (lower.includes('test') && (lower.includes('application') || lower.includes('full') || lower.includes('connection'))) {
            console.log(`  → run-command (test application/full/connection)`);
            return 'run-command';
        }

        // File creation actions
        if (lower.includes('create file') || lower.includes('write file') || lower.includes('generate file') ||
            lower.includes('new file') || lower.includes('add file')) {
            console.log(`  → create-file (contains create/write/generate file)`);
            return 'create-file';
        }

        // "Set up" usually means creating files (but only if not modifying existing file)
        if ((lower.includes('set up') || lower.includes('setup')) && !lower.includes('modify')) {
            console.log(`  → create-file (contains set up/setup, not modify)`);
            return 'create-file';
        }

        // Testing (only for generating test files, not running tests)
        if (lower.includes('test') &&
            (lower.includes('generate') || lower.includes('create') || lower.includes('write')) &&
            !lower.includes('application') && !lower.includes('full') && !lower.includes('run') && !lower.includes('connection')) {
            console.log(`  → generate-test (test file generation)`);
            return 'generate-test';
        }

        // Connect/integration usually means creating component files (but NOT if it's "test the connection")
        if ((lower.includes('connect') || lower.includes('integrate')) && !lower.includes('test')) {
            console.log(`  → create-file (contains connect/integrate, not test)`);
            return 'create-file';
        }

        // Fallback: if it mentions running/starting something
        if (lower.includes('run') || lower.includes('execute') || lower.includes('start')) {
            console.log(`  → run-command (fallback: contains run/execute/start)`);
            return 'run-command';
        }

        // No clear action detected - this should be an error
        console.log(`  → Unable to determine action for: "${description}"`);
        throw new Error(`Unable to determine appropriate action for step: "${description}". Please provide more specific instructions.`);
    }

    private inferParametersFromDescription(description: string, taskDescription: string): any {
        const lower = description.toLowerCase();

        // Handle Flask backend creation
        if (lower.includes('flask') && (lower.includes('create') || lower.includes('backend') || lower.includes('api'))) {
            return {
                filePath: 'app.py',
                needsAIGeneration: true,
                stepDescription: description,
                taskDescription
            };
        }

        // Handle specific file operations with quoted file paths (highest priority)
        const filePathMatch = description.match(/["']([^"']+\.(?:vue|py|js|ts|json|html|css))["']/);
        if (filePathMatch && (lower.includes('create') || lower.includes('write') || lower.includes('generate') || lower.includes('modify') || lower.includes('update'))) {
            let filePath = filePathMatch[1];

            // Convert absolute path to relative path if needed
            const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
            if (workspaceFolder && path.isAbsolute(filePath)) {
                const workspacePath = workspaceFolder.uri.fsPath;
                if (filePath.startsWith(workspacePath)) {
                    filePath = path.relative(workspacePath, filePath);
                    console.log(`🔧 AGENT: Converted absolute path to relative: ${filePath}`);
                }
            }

            return {
                filePath,
                needsAIGeneration: true,
                stepDescription: description,
                taskDescription
            };
        }

        // Handle component creation (only if no explicit file path was found)
        if (lower.includes('component') && lower.includes('vue') && !filePathMatch) {
            const componentName = this.extractComponentName(description) || 'DataComponent';
            return {
                filePath: `src/components/${componentName}.vue`,
                needsAIGeneration: true,
                stepDescription: description,
                taskDescription
            };
        }

        // Handle Vue frontend creation - create package.json first (other files created automatically)
        if (lower.includes('vue') && (lower.includes('create') || lower.includes('frontend') || lower.includes('project')) && !lower.includes('component')) {
            return {
                filePath: 'package.json',
                needsAIGeneration: true,
                stepDescription: description,
                taskDescription
            };
        }

        // Handle Python scripts
        if (lower.includes('python')) {
            if (lower.includes('create') || lower.includes('file')) {
                return {
                    filePath: 'generated_script.py',
                    content: this.generatePythonScript(taskDescription)
                };
            }
            if (lower.includes('run') || lower.includes('execute')) {
                return {
                    command: 'python',
                    args: ['generated_script.py']
                };
            }
        }

        // Handle npm/yarn commands
        if (lower.includes('install') && (lower.includes('npm') || lower.includes('yarn'))) {
            return {
                command: 'npm',
                args: ['install']
            };
        }

        // Handle specific run commands with quoted commands
        const runCommandMatch = description.match(/run command\s+["']([^"']+)["']/i);
        if (runCommandMatch) {
            const fullCommand = runCommandMatch[1];
            const parts = fullCommand.split(' ');
            return {
                command: parts[0],
                args: parts.slice(1)
            };
        }

        // Handle running applications
        if (lower.includes('run') || lower.includes('start')) {
            if (lower.includes('flask') || lower.includes('python')) {
                return {
                    command: 'python',
                    args: ['app.py']
                };
            }
            if (lower.includes('vue') || lower.includes('frontend')) {
                return {
                    command: 'npm',
                    args: ['run', 'serve']
                };
            }
            if (lower.includes('node') || lower.includes('server')) {
                // Extract file name if mentioned
                const nodeFileMatch = description.match(/node\s+([^\s"']+\.js)/i);
                if (nodeFileMatch) {
                    return {
                        command: 'node',
                        args: [nodeFileMatch[1]]
                    };
                }
                return {
                    command: 'node',
                    args: ['server.js']
                };
            }
        }

        // Handle file modifications - extract file path from description
        if (lower.includes('modify') || lower.includes('update') || lower.includes('edit')) {
            // Try to extract file path from quotes
            const filePathMatch = description.match(/["']([^"']+\.(?:vue|py|js|ts|json))["']/);
            if (filePathMatch) {
                let filePath = filePathMatch[1];

                // Convert absolute path to relative path if needed
                const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
                if (workspaceFolder && path.isAbsolute(filePath)) {
                    const workspacePath = workspaceFolder.uri.fsPath;
                    if (filePath.startsWith(workspacePath)) {
                        filePath = path.relative(workspacePath, filePath);
                        console.log(`🔧 AGENT: Converted absolute path to relative: ${filePath}`);
                    }
                }

                // Return parameters for AI-generated content (content will be generated later)
                return {
                    filePath,
                    needsAIGeneration: true,
                    stepDescription: description,
                    taskDescription
                };
            }
        }

        return {};
    }

    private generatePythonScript(taskDescription: string): string {
        const lower = taskDescription.toLowerCase();

        // Hello World script
        if (lower.includes('hello') && lower.includes('world')) {
            return `#!/usr/bin/env python3
"""
Python script that prints Hello World
"""

def main():
    """Main function that prints Hello World"""
    print("Hello world")

if __name__ == "__main__":
    main()
`;
        }

        // Count to 100 script
        if (lower.includes('count') && lower.includes('100')) {
            return `#!/usr/bin/env python3
"""
Generated Python script: ${taskDescription}
"""

def count_to_100():
    """Count from 1 to 100"""
    for i in range(1, 101):
        print(f"Count: {i}")
    print("Counting completed!")

if __name__ == "__main__":
    print("Starting to count to 100...")
    count_to_100()
    print("Script finished successfully!")
`;
        }

        // Data processing script
        if (lower.includes('process') && lower.includes('data')) {
            return `#!/usr/bin/env python3
"""
Data processing script
"""

def process_data(data):
    """Process a list of data items"""
    result = []
    for item in data:
        # Process each item (example: convert to uppercase if string)
        if isinstance(item, str):
            result.append(item.upper())
        else:
            result.append(item)
    return result

def main():
    """Main function"""
    sample_data = ["hello", "world", "python", "script"]
    processed = process_data(sample_data)
    print("Original data:", sample_data)
    print("Processed data:", processed)

if __name__ == "__main__":
    main()
`;
        }

        // Generic Python script (fallback)
        return `#!/usr/bin/env python3
"""
Generated Python script: ${taskDescription}
"""

def main():
    """Main function for the task"""
    print("Executing task: ${taskDescription.replace(/"/g, '\\"')}")
    # TODO: Implement the specific task logic here
    print("Task completed!")

if __name__ == "__main__":
    main()
`;
    }



    // Removed: All hardcoded package.json generation - now using AI-generated content

    // Removed: All hardcoded component generation - now using AI-generated content

    // Removed: All hardcoded app generation - now using AI-generated content

    private extractComponentName(description: string): string | null {
        // Try to extract from "ComponentName.vue" pattern first
        const fileMatch = description.match(/(\w+)\.vue/i);
        if (fileMatch) {
            return fileMatch[1].charAt(0).toUpperCase() + fileMatch[1].slice(1);
        }

        // Fallback to "ComponentNamecomponent" pattern
        const componentMatch = description.match(/(\w+)component/i);
        return componentMatch ? componentMatch[1].charAt(0).toUpperCase() + componentMatch[1].slice(1) : null;
    }



    private async executeTaskSteps(task: AgentTask): Promise<void> {
        for (let i = 0; i < task.steps.length; i++) {
            const step = task.steps[i];
            if (!this.isEnabled) {
                throw new Error('Agent mode was disabled during execution');
            }

            this.reportProgress(`🔧 Step ${i + 1}/${task.steps.length}: ${step.description.substring(0, 50)}...`);
            step.status = 'in-progress';

            try {
                // Create checkpoint before executing step
                step.checkpoint = await this.createCheckpoint();

                // Execute the step
                await this.executeStep(step);

                step.status = 'completed';
                this.reportProgress(`✅ Step ${i + 1} completed successfully`);
                
                // Ask for confirmation if required
                if (this.shouldRequestConfirmation(step)) {
                    const confirmed = await this.requestStepConfirmation(step);
                    if (!confirmed) {
                        await this.rollbackToCheckpoint(step.checkpoint);
                        throw new Error('Step cancelled by user');
                    }
                }
            } catch (error) {
                step.status = 'failed';
                step.result = error instanceof Error ? error.message : String(error);
                
                // Rollback if checkpoint exists
                if (step.checkpoint) {
                    await this.rollbackToCheckpoint(step.checkpoint);
                }
                
                throw error;
            }
        }
    }

    private async executeStep(step: AgentStep): Promise<void> {
        switch (step.action) {
            case 'create-file':
                await this.executeCreateFile(step);
                break;
            case 'modify-file':
                await this.executeModifyFile(step);
                break;
            case 'run-command':
                await this.executeRunCommand(step);
                break;
            case 'analyze-code':
                await this.executeAnalyzeCode(step);
                break;
            case 'generate-test':
                await this.executeGenerateTest(step);
                break;
            default:
                throw new Error(`Unknown step action: ${step.action}`);
        }
    }

    private async executeCreateFile(step: AgentStep): Promise<void> {
        const { filePath, content, needsAIGeneration, stepDescription, taskDescription } = step.parameters;

        console.log('🔧 AGENT: Executing create-file step');
        console.log('🔧 AGENT: Parameters:', { filePath, hasContent: !!content, needsAIGeneration });

        if (!filePath) {
            throw new Error('Missing filePath parameter');
        }

        let finalContent = content;

        // Generate content using AI if needed
        if (needsAIGeneration && !content) {
            console.log('🤖 AGENT: Generating content using AI...');
            this.reportProgress(`🤖 Generating content for ${path.basename(filePath)}...`);
            finalContent = await this.generateFileContentWithAI(filePath, stepDescription, taskDescription);
            if (finalContent && finalContent.length > 0) {
                console.log('🤖 AGENT: AI content generated successfully');
                console.log(`🤖 AGENT: Generated content length: ${finalContent.length} characters`);
                this.reportProgress(`✅ Content generated (${finalContent.length} chars)`);
            } else {
                console.log('🚨 AGENT: AI content generation returned empty result');
                this.reportProgress('❌ AI content generation failed');
            }
        }

        if (!finalContent) {
            throw new Error('Missing content parameter and AI generation failed');
        }

        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            throw new Error('No workspace folder available');
        }

        const fullPath = path.join(workspaceFolder.uri.fsPath, filePath);

        // Ensure directory exists
        const dir = path.dirname(fullPath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }

        // Create the file
        fs.writeFileSync(fullPath, finalContent, 'utf8');
        step.result = `Created file: ${filePath}`;

        console.log(`File created: ${fullPath}`);
        console.log(`File content length: ${finalContent.length} characters`);

        // Removed: Hardcoded Vue project setup - now using AI-generated content for all files

        // Open the file in editor
        const document = await vscode.workspace.openTextDocument(fullPath);
        await vscode.window.showTextDocument(document);

        // Show success message
        vscode.window.showInformationMessage(`✅ Created: ${filePath}`);
    }

    // Removed: All hardcoded Vue project setup - now using AI-generated content for all files

    private async executeModifyFile(step: AgentStep): Promise<void> {
        const { filePath, changes, content, needsAIGeneration, stepDescription, taskDescription } = step.parameters;

        console.log('🔧 AGENT: Executing modify-file step');
        console.log('🔧 AGENT: Parameters:', { filePath, hasChanges: !!changes, hasContent: !!content, needsAIGeneration });

        if (!filePath) {
            throw new Error('Missing filePath parameter');
        }

        // Check if this can be done as a targeted snippet modification
        const canUseSnippetModification = await this.canUseSnippetModification(filePath, stepDescription, taskDescription);

        if (canUseSnippetModification) {
            this.reportProgress(`🎯 Analyzing ${path.basename(filePath)} for targeted modification...`);
            await this.executeSnippetModification(step);
            return;
        }

        let finalContent = content;

        // Generate content using AI if needed
        if (needsAIGeneration && !content) {
            console.log('🤖 AGENT: Generating content using AI...');
            this.reportProgress(`🤖 Generating full content for ${path.basename(filePath)}...`);
            finalContent = await this.generateFileContentWithAI(filePath, stepDescription, taskDescription);
            if (finalContent && finalContent.length > 0) {
                console.log('🤖 AGENT: AI content generated successfully');
                console.log(`🤖 AGENT: Generated content length: ${finalContent.length} characters`);
                this.reportProgress(`✅ Content generated (${finalContent.length} chars)`);
            } else {
                console.log('🚨 AGENT: AI content generation returned empty result');
                this.reportProgress('❌ AI content generation failed');
            }
        }

        if (!changes && !finalContent) {
            throw new Error('Missing changes or content parameters');
        }

        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            throw new Error('No workspace folder available');
        }

        console.log('🔧 AGENT: Workspace folder:', workspaceFolder.uri.fsPath);
        console.log('🔧 AGENT: Relative file path:', filePath);

        const fullPath = path.join(workspaceFolder.uri.fsPath, filePath);
        console.log('🔧 AGENT: Full file path:', fullPath);
        console.log('🔧 AGENT: File exists:', fs.existsSync(fullPath));

        if (!fs.existsSync(fullPath)) {
            console.log('🔧 AGENT: File does not exist, creating it first...');
            // Create the file with the content if it doesn't exist
            const dir = path.dirname(fullPath);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
            fs.writeFileSync(fullPath, finalContent || '');
            console.log('🔧 AGENT: File created successfully');
        }

        // Apply changes using VS Code's edit API
        const document = await vscode.workspace.openTextDocument(fullPath);
        const editor = await vscode.window.showTextDocument(document);

        await editor.edit(editBuilder => {
            if (changes) {
                // Apply specific changes
                for (const change of changes) {
                    const range = new vscode.Range(
                        change.startLine - 1,
                        change.startColumn || 0,
                        change.endLine - 1,
                        change.endColumn || document.lineAt(change.endLine - 1).text.length
                    );
                    editBuilder.replace(range, change.newText);
                }
            } else if (finalContent !== undefined) {
                // Replace entire file content
                const fullRange = new vscode.Range(
                    0, 0,
                    document.lineCount - 1,
                    document.lineAt(document.lineCount - 1).text.length
                );
                editBuilder.replace(fullRange, finalContent);
            }
        });

        step.result = `Modified file: ${filePath}`;
    }

    private async executeRunCommand(step: AgentStep): Promise<void> {
        const { command, args = [], cwd } = step.parameters;

        if (!command) {
            throw new Error('Missing command parameter');
        }

        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        const workingDir = cwd || workspaceFolder?.uri.fsPath || process.cwd();

        console.log(`Executing command: ${command} ${args.join(' ')} in ${workingDir}`);

        // Create and show a terminal for the command
        const terminal = vscode.window.createTerminal({
            name: 'Morpheus Agent',
            cwd: workingDir
        });

        // Show the terminal
        terminal.show();

        // Build the command string
        const fullCommand = args.length > 0 ? `${command} ${args.join(' ')}` : command;

        // Send the command to the terminal
        terminal.sendText(fullCommand);

        // For Python scripts, add some helpful output
        if (command === 'python' && args.length > 0) {
            // Wait a moment then add a completion message
            setTimeout(() => {
                terminal.sendText(`echo "Script execution completed!"`);
            }, 2000);
        }

        step.result = `Command executed in terminal: ${fullCommand}`;

        // Show success message
        vscode.window.showInformationMessage(`✅ Executed: ${fullCommand}`);
    }

    private async executeAnalyzeCode(step: AgentStep): Promise<void> {
        const { filePath } = step.parameters;
        
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            throw new Error('No workspace folder available');
        }

        const targetPath = filePath ? 
            path.join(workspaceFolder.uri.fsPath, filePath) : 
            workspaceFolder.uri.fsPath;

        const analysis = this.contextEngine.getRelevantContext(targetPath, 50000);
        step.result = `Code analysis completed for: ${filePath || 'workspace'}`;
    }

    private async executeGenerateTest(step: AgentStep): Promise<void> {
        const { filePath, testType = 'unit' } = step.parameters;
        
        if (!filePath) {
            throw new Error('Missing filePath parameter');
        }

        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            throw new Error('No workspace folder available');
        }

        const fullPath = path.join(workspaceFolder.uri.fsPath, filePath);
        
        if (!fs.existsSync(fullPath)) {
            throw new Error(`File does not exist: ${filePath}`);
        }

        const sourceCode = fs.readFileSync(fullPath, 'utf8');
        const testCode = await this.generateTestCode(sourceCode, testType, filePath);
        
        // Create test file
        const testFilePath = this.getTestFilePath(filePath);
        const testFullPath = path.join(workspaceFolder.uri.fsPath, testFilePath);
        
        const testDir = path.dirname(testFullPath);
        if (!fs.existsSync(testDir)) {
            fs.mkdirSync(testDir, { recursive: true });
        }

        fs.writeFileSync(testFullPath, testCode, 'utf8');
        step.result = `Generated ${testType} tests: ${testFilePath}`;

        // Open the test file
        const document = await vscode.workspace.openTextDocument(testFullPath);
        await vscode.window.showTextDocument(document);
    }

    private async generateTestCode(sourceCode: string, testType: string, filePath: string): Promise<string> {
        const prompt = `Generate ${testType} tests for the following code:

File: ${filePath}
Code:
${sourceCode}

Please generate comprehensive tests that cover:
1. Normal functionality
2. Edge cases
3. Error conditions

Use appropriate testing framework (pytest for Python, Jest for JavaScript/TypeScript).`;

        const response = await this.aiService.generateChatResponse([
            {
                role: 'system',
                content: 'You are an expert test generator. Create comprehensive, well-structured tests.'
            },
            {
                role: 'user',
                content: prompt
            }
        ]);

        if (!response) {
            throw new Error('Failed to generate test code');
        }

        return response;
    }

    private getTestFilePath(filePath: string): string {
        const ext = path.extname(filePath);
        const baseName = path.basename(filePath, ext);
        const dir = path.dirname(filePath);
        
        // Create test directory structure
        const testDir = path.join(dir, 'tests');
        return path.join(testDir, `test_${baseName}${ext}`);
    }

    private shouldRequestConfirmation(step: AgentStep): boolean {
        const config = vscode.workspace.getConfiguration('morpheus');
        const requireConfirmation = config.get('agentConfirmationRequired', true);
        
        // Always require confirmation for file operations
        return requireConfirmation || ['create-file', 'modify-file'].includes(step.action);
    }

    private async requestStepConfirmation(step: AgentStep): Promise<boolean> {
        const action = await vscode.window.showInformationMessage(
            `Agent completed: ${step.description}\nResult: ${step.result || 'Success'}`,
            'Continue',
            'Cancel',
            'View Changes'
        );

        switch (action) {
            case 'Continue':
                return true;
            case 'View Changes':
                await this.showStepChanges(step);
                return await this.requestStepConfirmation(step); // Ask again after viewing
            default:
                return false;
        }
    }

    private async showStepChanges(step: AgentStep): Promise<void> {
        // Implementation would show diff or changes made by the step
        vscode.window.showInformationMessage(`Step: ${step.description}\nResult: ${step.result}`);
    }

    private async createCheckpoint(): Promise<string> {
        // Simple checkpoint implementation - could be enhanced with git integration
        const checkpointId = `checkpoint_${Date.now()}`;
        // In a real implementation, this would create a git stash or similar
        return checkpointId;
    }

    private async rollbackToCheckpoint(checkpointId: string): Promise<void> {
        // Implementation would restore from checkpoint
        console.log(`Rolling back to checkpoint: ${checkpointId}`);
    }

    private pauseCurrentTask(): void {
        if (this.currentTask) {
            this.currentTask.status = 'pending';
            vscode.window.showInformationMessage('Current task paused');
        }
    }

    public showPendingChanges(): void {
        if (!this.currentTask) {
            vscode.window.showInformationMessage('No active agent task');
            return;
        }

        const completedSteps = this.currentTask.steps.filter(s => s.status === 'completed');
        const message = `Task: ${this.currentTask.description}\nCompleted steps: ${completedSteps.length}/${this.currentTask.steps.length}`;
        
        vscode.window.showInformationMessage(message, 'View Details').then(action => {
            if (action === 'View Details') {
                this.showTaskDetails(this.currentTask!);
            }
        });
    }

    private showTaskDetails(task: AgentTask): void {
        // Create a webview or output channel to show task details
        const channel = vscode.window.createOutputChannel('Morpheus Agent');
        channel.clear();
        channel.appendLine(`Task: ${task.description}`);
        channel.appendLine(`Status: ${task.status}`);
        channel.appendLine('');
        
        task.steps.forEach((step, index) => {
            channel.appendLine(`Step ${index + 1}: ${step.description}`);
            channel.appendLine(`  Status: ${step.status}`);
            if (step.result) {
                channel.appendLine(`  Result: ${step.result}`);
            }
            channel.appendLine('');
        });
        
        channel.show();
    }

    private showAgentPanel(): void {
        // Could create a dedicated webview panel for agent control
        vscode.window.showInformationMessage(
            'Agent Mode is now active. Use the chat panel to give me tasks!',
            'Open Chat'
        ).then(action => {
            if (action === 'Open Chat') {
                vscode.commands.executeCommand('morpheus.openChat');
            }
        });
    }

    private generateTaskId(): string {
        return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    private async canUseSnippetModification(filePath: string, stepDescription: string, taskDescription: string): Promise<boolean> {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            return false;
        }

        const fullPath = path.join(workspaceFolder.uri.fsPath, filePath);
        if (!fs.existsSync(fullPath)) {
            return false; // Can't modify snippet if file doesn't exist
        }

        const currentContent = fs.readFileSync(fullPath, 'utf8');
        const lines = currentContent.split('\n');

        // Don't use snippet modification for very small files (less than 10 lines)
        if (lines.length < 10) {
            return false;
        }

        const lower = stepDescription.toLowerCase();

        // Use snippet modification for specific, targeted changes
        const snippetKeywords = [
            'add function', 'add method', 'add property', 'add field', 'add variable',
            'update function', 'update method', 'modify function', 'modify method',
            'fix bug', 'fix error', 'fix issue', 'fix typo', 'correct',
            'add import', 'add dependency', 'import', 'require',
            'add route', 'add endpoint', 'add handler', 'add api',
            'add component', 'add class', 'add interface', 'add type',
            'change parameter', 'update parameter', 'modify parameter',
            'add validation', 'add check', 'add test', 'add condition',
            'refactor', 'optimize', 'improve', 'enhance',
            'remove function', 'delete method', 'remove import',
            'rename', 'move', 'extract'
        ];

        // Don't use snippet modification for complete rewrites
        const fullRewriteKeywords = [
            'rewrite', 'recreate', 'completely change', 'start over',
            'new structure', 'restructure completely', 'rebuild',
            'create new', 'generate new', 'replace entire'
        ];

        const hasSnippetKeyword = snippetKeywords.some(keyword => lower.includes(keyword));
        const hasFullRewriteKeyword = fullRewriteKeywords.some(keyword => lower.includes(keyword));

        return hasSnippetKeyword && !hasFullRewriteKeyword;
    }

    private async executeSnippetModification(step: AgentStep): Promise<void> {
        const { filePath, stepDescription, taskDescription } = step.parameters;

        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            throw new Error('No workspace folder available');
        }

        const fullPath = path.join(workspaceFolder.uri.fsPath, filePath);
        const currentContent = fs.readFileSync(fullPath, 'utf8');

        console.log('🎯 AGENT: Executing snippet modification');
        this.reportProgress(`🎯 Analyzing code structure...`);

        // Generate targeted modification using AI
        const modification = await this.generateSnippetModificationWithAI(
            filePath,
            currentContent,
            stepDescription,
            taskDescription
        );

        if (!modification) {
            console.log('🚨 AGENT: Snippet modification failed, falling back to full file generation');
            this.reportProgress('⚠️ Falling back to full file modification...');

            // Fall back to full file modification
            const finalContent = await this.generateFileContentWithAI(filePath, stepDescription, taskDescription);
            await this.applyFullFileModification(filePath, finalContent);
            step.result = `Modified file (full): ${filePath}`;
            return;
        }

        this.reportProgress(`🔧 Applying targeted changes...`);
        await this.applySnippetModification(filePath, modification);
        step.result = `Modified file (snippet): ${filePath}`;
    }

    private async generateSnippetModificationWithAI(
        filePath: string,
        currentContent: string,
        stepDescription: string,
        taskDescription: string
    ): Promise<SnippetModification | null> {
        try {
            const fileExtension = path.extname(filePath);
            const fileName = path.basename(filePath);

            const prompt = `You are a precise code editor. Analyze the current file and provide ONLY the specific changes needed.

TASK: ${taskDescription}
STEP: ${stepDescription}
FILE: ${fileName}
TYPE: ${fileExtension}

CURRENT FILE CONTENT:
\`\`\`${fileExtension.substring(1)}
${currentContent}
\`\`\`

Analyze the code and provide a JSON response with the specific modification needed. Use this exact format:

\`\`\`json
{
  "modificationType": "insert" | "replace" | "delete",
  "targetLocation": {
    "startLine": number,
    "endLine": number,
    "description": "brief description of what you're targeting"
  },
  "newContent": "the exact code to insert/replace with",
  "reasoning": "brief explanation of the change"
}
\`\`\`

MODIFICATION TYPES:
- "insert": Add new code after the specified line (startLine = endLine = line number after which to insert)
- "replace": Replace existing code in the specified range (startLine to endLine inclusive)
- "delete": Remove code in the specified range (startLine to endLine inclusive, newContent should be empty)

REQUIREMENTS:
- Line numbers are 1-based (first line is 1, not 0)
- Only provide the minimal change needed to accomplish the task
- Preserve existing code style, indentation, and formatting
- Match the existing naming conventions and patterns
- If the change requires multiple separate modifications or is too complex for a single targeted change, respond with just the word "null"
- Ensure the modification will result in syntactically correct code

EXAMPLES:
- To add a new function: use "insert" after the last function
- To fix a bug in an existing function: use "replace" for just that function
- To add an import: use "insert" at the top with other imports
- To remove unused code: use "delete" for the specific lines`;

            console.log('🤖 AGENT: Requesting snippet modification from AI...');
            this.reportProgress('🤖 Analyzing modification requirements...');

            const response = await this.aiService.generateChatResponse([
                { role: 'user', content: prompt }
            ]);

            if (!response) {
                console.log('🚨 AGENT: AI response was empty');
                return null;
            }

            // Check if AI responded with "null" indicating the change is too complex
            if (response.trim().toLowerCase() === 'null') {
                console.log('🚨 AGENT: AI indicated change is too complex for snippet modification');
                return null;
            }

            // Try to parse JSON response
            let modification: SnippetModification;
            try {
                // Extract JSON from response if it's wrapped in markdown
                const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/) || response.match(/```\s*([\s\S]*?)\s*```/);
                const jsonStr = jsonMatch ? jsonMatch[1].trim() : response.trim();

                // Check if the extracted content is just "null"
                if (jsonStr.toLowerCase() === 'null') {
                    console.log('🚨 AGENT: AI indicated change is too complex for snippet modification');
                    return null;
                }

                modification = JSON.parse(jsonStr);
            } catch (parseError) {
                console.log('🚨 AGENT: Failed to parse AI response as JSON:', parseError);
                console.log('🚨 AGENT: Raw response:', response);
                return null;
            }

            // Validate the modification structure
            if (!this.isValidSnippetModification(modification)) {
                console.log('🚨 AGENT: Invalid snippet modification structure:', modification);
                return null;
            }

            console.log('✅ AGENT: Valid snippet modification generated:', modification);
            return modification;

        } catch (error) {
            console.error('🚨 AGENT: Error generating snippet modification:', error);
            return null;
        }
    }

    private isValidSnippetModification(modification: any): modification is SnippetModification {
        return (
            modification &&
            typeof modification === 'object' &&
            ['insert', 'replace', 'delete'].includes(modification.modificationType) &&
            modification.targetLocation &&
            typeof modification.targetLocation.startLine === 'number' &&
            typeof modification.targetLocation.endLine === 'number' &&
            typeof modification.targetLocation.description === 'string' &&
            typeof modification.newContent === 'string' &&
            typeof modification.reasoning === 'string' &&
            modification.targetLocation.startLine > 0 &&
            modification.targetLocation.endLine > 0 &&
            modification.targetLocation.startLine <= modification.targetLocation.endLine
        );
    }

    private async applySnippetModification(filePath: string, modification: SnippetModification): Promise<void> {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            throw new Error('No workspace folder available');
        }

        const fullPath = path.join(workspaceFolder.uri.fsPath, filePath);
        const document = await vscode.workspace.openTextDocument(fullPath);
        const editor = await vscode.window.showTextDocument(document);

        console.log(`🎯 AGENT: Applying ${modification.modificationType} modification:`, modification.reasoning);
        this.reportProgress(`🔧 ${modification.modificationType}: ${modification.reasoning}`);

        await editor.edit(editBuilder => {
            const { startLine, endLine } = modification.targetLocation;

            switch (modification.modificationType) {
                case 'insert':
                    // Insert after the specified line
                    const insertPosition = new vscode.Position(startLine, 0);
                    editBuilder.insert(insertPosition, modification.newContent + '\n');
                    break;

                case 'replace':
                    // Replace the specified range
                    const replaceRange = new vscode.Range(
                        startLine - 1, 0,
                        endLine - 1, document.lineAt(endLine - 1).text.length
                    );
                    editBuilder.replace(replaceRange, modification.newContent);
                    break;

                case 'delete':
                    // Delete the specified range
                    const deleteRange = new vscode.Range(
                        startLine - 1, 0,
                        endLine, 0  // Include the newline of the last line
                    );
                    editBuilder.delete(deleteRange);
                    break;
            }
        });

        console.log(`✅ AGENT: Successfully applied ${modification.modificationType} modification`);
        this.reportProgress(`✅ Applied ${modification.modificationType} successfully`);
    }

    private async applyFullFileModification(filePath: string, content: string): Promise<void> {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            throw new Error('No workspace folder available');
        }

        const fullPath = path.join(workspaceFolder.uri.fsPath, filePath);
        const document = await vscode.workspace.openTextDocument(fullPath);
        const editor = await vscode.window.showTextDocument(document);

        await editor.edit(editBuilder => {
            const fullRange = new vscode.Range(
                0, 0,
                document.lineCount - 1,
                document.lineAt(document.lineCount - 1).text.length
            );
            editBuilder.replace(fullRange, content);
        });

        console.log(`✅ AGENT: Successfully applied full file modification`);
        this.reportProgress(`✅ Applied full file modification`);
    }

    private async generateFileContentWithAI(filePath: string, stepDescription: string, taskDescription: string): Promise<string> {
        try {
            // Check cache first
            const cacheKey = this.generateCacheKey(filePath, stepDescription, taskDescription);
            const cached = this.getCachedContent(cacheKey);
            if (cached) {
                console.log(`🚀 AGENT: Using cached content for ${filePath}`);
                this.reportProgress('📋 Using cached content');
                return cached;
            }



            // Get current file content if it exists
            const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
            let currentContent = '';

            if (workspaceFolder) {
                const fullPath = path.join(workspaceFolder.uri.fsPath, filePath);
                if (fs.existsSync(fullPath)) {
                    currentContent = fs.readFileSync(fullPath, 'utf8');
                }
            }

            // Build context for AI
            const fileExtension = path.extname(filePath);
            const fileName = path.basename(filePath);

            const prompt = `You are a code generator. Generate the complete file content for the following task.

TASK: ${taskDescription}
STEP: ${stepDescription}
FILE: ${fileName}
TYPE: ${fileExtension}

${currentContent ? `CURRENT CONTENT:
\`\`\`
${currentContent}
\`\`\`

UPDATE the above content to complete the task.` : 'CREATE new file content from scratch.'}

REQUIREMENTS:
- Generate ONLY the complete file content
- NO markdown formatting, NO code blocks, NO triple backticks
- NO explanations, comments, or descriptions outside the code
- Start directly with the code content
- Make sure the code is functional and follows best practices
- For Vue components, include template, script, and style sections
- For login components, include username/password inputs and form handling
- Generate COMPLETE code - do not truncate or leave incomplete sections
- Include all necessary imports, methods, and closing tags
- For backend endpoints, include all CRUD operations (GET, POST, PUT, DELETE)

Generate the complete file content now (start directly with the code):`;

            // Retry logic for timeout errors
            let response: string | null = null;
            const maxRetries = 2;

            for (let attempt = 1; attempt <= maxRetries; attempt++) {
                try {
                    console.log(`🤖 AGENT: Generation attempt ${attempt}/${maxRetries}`);
                    this.reportProgress(`🤖 AI generation attempt ${attempt}/${maxRetries}...`);

                    response = await this.aiService.generateChatResponse([
                        {
                            role: 'user',
                            content: prompt
                        }
                    ], {
                        maxTokens: 4000,  // Increased from default to prevent truncation
                        temperature: 0.7
                    });

                    if (response) {
                        console.log(`🤖 AGENT: Successfully generated content on attempt ${attempt}`);
                        this.reportProgress(`✅ AI generation successful on attempt ${attempt}`);
                        break; // Success, exit retry loop
                    }
                } catch (error: any) {
                    console.log(`🚨 AGENT: Attempt ${attempt} failed:`, error.message);

                    // If this is a timeout error and we have retries left, continue
                    if (error.code === 'ECONNABORTED' && attempt < maxRetries) {
                        console.log(`🔄 AGENT: Retrying due to timeout (attempt ${attempt + 1}/${maxRetries})`);
                        continue;
                    }

                    // If it's the last attempt or not a timeout, throw the error
                    throw error;
                }
            }

            if (!response) {
                throw new Error('AI service returned empty response after all retry attempts');
            }

            // Clean up the response to remove markdown formatting
            let cleanedResponse = response.trim();

            // Remove markdown code blocks (```language and ```)
            cleanedResponse = cleanedResponse.replace(/^```[\w]*\n?/gm, '');
            cleanedResponse = cleanedResponse.replace(/\n?```$/gm, '');
            cleanedResponse = cleanedResponse.replace(/```/g, '');

            // Remove any leading/trailing whitespace after cleanup
            cleanedResponse = cleanedResponse.trim();

            console.log(`🧹 AGENT: Cleaned response length: ${cleanedResponse.length} characters`);

            // Cache the result for future use
            this.cacheContent(cacheKey, cleanedResponse);

            return cleanedResponse;

        } catch (error) {
            console.error('🚨 AGENT: Failed to generate AI content:', error);



            throw new Error(`Failed to generate content with AI: ${error instanceof Error ? error.message : String(error)}`);
        }
    }



    private generateCacheKey(filePath: string, stepDescription: string, taskDescription: string): string {
        const content = `${filePath}:${stepDescription}:${taskDescription}`;
        return Buffer.from(content).toString('base64').substring(0, 32);
    }

    private getCachedContent(key: string): string | null {
        const cached = this.contentCache.get(key);
        if (cached && Date.now() - cached.timestamp < this.cacheExpirationMs) {
            return cached.content;
        }
        if (cached) {
            this.contentCache.delete(key);
        }
        return null;
    }

    private cacheContent(key: string, content: string): void {
        this.contentCache.set(key, {
            content,
            timestamp: Date.now()
        });

        // Clean up old cache entries periodically
        if (this.contentCache.size > 50) {
            this.cleanupCache();
        }
    }

    private cleanupCache(): void {
        const now = Date.now();
        for (const [key, value] of this.contentCache.entries()) {
            if (now - value.timestamp > this.cacheExpirationMs) {
                this.contentCache.delete(key);
            }
        }
    }

    public dispose(): void {
        this.statusBarItem.dispose();
        this.contentCache.clear();
    }
}
