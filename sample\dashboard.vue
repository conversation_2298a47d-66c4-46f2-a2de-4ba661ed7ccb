<template>
  <div class="business-dashboard">
    <header class="dashboard-header">
      <h1>Business Analytics Dashboard</h1>
      <div class="date-range-selector">
        <input 
          v-model="startDate" 
          type="date" 
          @change="updateReports"
        />
        <span>to</span>
        <input 
          v-model="endDate" 
          type="date" 
          @change="updateReports"
        />
      </div>
    </header>

    <main class="dashboard-content">
      <div class="metrics-grid">
        <div class="metric-card">
          <h3>Total Revenue</h3>
          <div class="metric-value">{{ formatCurrency(totalRevenue) }}</div>
          <div class="metric-change" :class="revenueChangeClass">
            {{ revenueChange }}%
          </div>
        </div>

        <div class="metric-card">
          <h3>Orders</h3>
          <div class="metric-value">{{ totalOrders }}</div>
          <div class="metric-change" :class="ordersChangeClass">
            {{ ordersChange }}%
          </div>
        </div>

        <div class="metric-card">
          <h3>Customers</h3>
          <div class="metric-value">{{ totalCustomers }}</div>
          <div class="metric-change" :class="customersChangeClass">
            {{ customersChange }}%
          </div>
        </div>

        <div class="metric-card">
          <h3>Avg Order Value</h3>
          <div class="metric-value">{{ formatCurrency(avgOrderValue) }}</div>
          <div class="metric-change" :class="aovChangeClass">
            {{ aovChange }}%
          </div>
        </div>
      </div>

      <div class="charts-section">
        <div class="chart-container">
          <h3>Sales Trend</h3>
          <canvas ref="salesChart"></canvas>
        </div>

        <div class="chart-container">
          <h3>Top Products</h3>
          <div class="product-list">
            <div 
              v-for="product in topProducts" 
              :key="product.id"
              class="product-item"
            >
              <span class="product-name">{{ product.name }}</span>
              <span class="product-sales">{{ formatCurrency(product.sales) }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="reports-section">
        <h3>Generate Reports</h3>
        <div class="report-buttons">
          <button @click="generateSalesReport" :disabled="isGenerating">
            {{ isGenerating ? 'Generating...' : 'Sales Report' }}
          </button>
          <button @click="generateCustomerReport" :disabled="isGenerating">
            Customer Analysis
          </button>
          <button @click="generateInventoryReport" :disabled="isGenerating">
            Inventory Report
          </button>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useBusinessData } from '@/composables/useBusinessData'
import { useReportGenerator } from '@/composables/useReportGenerator'

export default {
  name: 'BusinessDashboard',
  setup() {
    // Reactive data
    const startDate = ref('')
    const endDate = ref('')
    const isGenerating = ref(false)

    // Composables
    const { 
      metrics, 
      topProducts, 
      salesData, 
      fetchMetrics,
      fetchTopProducts,
      fetchSalesData 
    } = useBusinessData()

    const { 
      generateReport 
    } = useReportGenerator()

    // Computed properties
    const totalRevenue = computed(() => metrics.value?.totalRevenue || 0)
    const totalOrders = computed(() => metrics.value?.totalOrders || 0)
    const totalCustomers = computed(() => metrics.value?.totalCustomers || 0)
    const avgOrderValue = computed(() => 
      totalOrders.value > 0 ? totalRevenue.value / totalOrders.value : 0
    )

    const revenueChange = computed(() => metrics.value?.revenueChange || 0)
    const ordersChange = computed(() => metrics.value?.ordersChange || 0)
    const customersChange = computed(() => metrics.value?.customersChange || 0)
    const aovChange = computed(() => metrics.value?.aovChange || 0)

    // CSS classes for change indicators
    const revenueChangeClass = computed(() => 
      revenueChange.value >= 0 ? 'positive' : 'negative'
    )
    const ordersChangeClass = computed(() => 
      ordersChange.value >= 0 ? 'positive' : 'negative'
    )
    const customersChangeClass = computed(() => 
      customersChange.value >= 0 ? 'positive' : 'negative'
    )
    const aovChangeClass = computed(() => 
      aovChange.value >= 0 ? 'positive' : 'negative'
    )

    // Methods
    const formatCurrency = (amount) => {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
      }).format(amount)
    }

    const updateReports = async () => {
      if (startDate.value && endDate.value) {
        await Promise.all([
          fetchMetrics(startDate.value, endDate.value),
          fetchTopProducts(startDate.value, endDate.value),
          fetchSalesData(startDate.value, endDate.value)
        ])
      }
    }

    const generateSalesReport = async () => {
      isGenerating.value = true
      try {
        await generateReport('sales', {
          startDate: startDate.value,
          endDate: endDate.value,
          format: 'csv'
        })
      } finally {
        isGenerating.value = false
      }
    }

    const generateCustomerReport = async () => {
      // Morpheus can help complete this method
    }

    const generateInventoryReport = async () => {
      // Morpheus can help complete this method
    }

    // Initialize dates
    const initializeDates = () => {
      const today = new Date()
      const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
      
      endDate.value = today.toISOString().split('T')[0]
      startDate.value = thirtyDaysAgo.toISOString().split('T')[0]
    }

    // Lifecycle
    onMounted(() => {
      initializeDates()
      updateReports()
    })

    // Watchers
    watch([startDate, endDate], updateReports)

    return {
      startDate,
      endDate,
      isGenerating,
      totalRevenue,
      totalOrders,
      totalCustomers,
      avgOrderValue,
      revenueChange,
      ordersChange,
      customersChange,
      aovChange,
      revenueChangeClass,
      ordersChangeClass,
      customersChangeClass,
      aovChangeClass,
      topProducts,
      formatCurrency,
      updateReports,
      generateSalesReport,
      generateCustomerReport,
      generateInventoryReport
    }
  }
}
</script>

<style scoped>
.business-dashboard {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.date-range-selector {
  display: flex;
  align-items: center;
  gap: 10px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.metric-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.metric-value {
  font-size: 2em;
  font-weight: bold;
  margin: 10px 0;
}

.metric-change {
  font-size: 0.9em;
  font-weight: 500;
}

.metric-change.positive {
  color: #10b981;
}

.metric-change.negative {
  color: #ef4444;
}

.charts-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.chart-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.product-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.product-item {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  background: #f9fafb;
  border-radius: 4px;
}

.reports-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.report-buttons {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.report-buttons button {
  padding: 10px 20px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.report-buttons button:hover:not(:disabled) {
  background: #2563eb;
}

.report-buttons button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}
</style>
