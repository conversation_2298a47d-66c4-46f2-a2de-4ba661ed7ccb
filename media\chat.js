// Morpheus Chat Panel JavaScript
(function() {
    const vscode = acquireVsCodeApi();
    
    // DOM elements
    const messageInput = document.getElementById('messageInput');
    const sendBtn = document.getElementById('sendBtn');
    const messagesContainer = document.getElementById('messages');
    const typingIndicator = document.getElementById('typingIndicator');
    const clearBtn = document.getElementById('clearBtn');
    const exportBtn = document.getElementById('exportBtn');
    const contextBtn = document.getElementById('contextBtn');
    const chatContainer = document.getElementById('chatContainer');

    // State
    let isWaitingForResponse = false;

    // Initialize
    init();

    function init() {
        setupEventListeners();
        messageInput.focus();
        
        // Restore previous state if available
        const state = vscode.getState();
        if (state && state.messages) {
            state.messages.forEach(msg => addMessage(msg.content, msg.role, msg.timestamp));
        }
    }

    function setupEventListeners() {
        // Send message on button click
        sendBtn.addEventListener('click', sendMessage);
        
        // Send message on Enter (Shift+Enter for new line)
        messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // Auto-resize textarea
        messageInput.addEventListener('input', autoResizeTextarea);

        // Header button actions
        clearBtn.addEventListener('click', clearChat);
        exportBtn.addEventListener('click', exportChat);
        contextBtn.addEventListener('click', getContext);

        // Listen for messages from extension
        window.addEventListener('message', handleExtensionMessage);

        // Add event delegation for code block buttons
        document.addEventListener('click', handleCodeBlockActions);
    }

    function sendMessage() {
        const message = messageInput.value.trim();
        if (!message || isWaitingForResponse) {
            return;
        }

        // Clear input and disable send button
        messageInput.value = '';
        setWaitingState(true);
        autoResizeTextarea();

        // Send message to extension
        vscode.postMessage({
            type: 'sendMessage',
            message: message
        });
    }

    function handleExtensionMessage(event) {
        const message = event.data;
        
        switch (message.type) {
            case 'userMessage':
                addMessage(message.message, 'user', message.timestamp);
                saveState();
                break;
                
            case 'aiResponse':
                addMessage(message.message, 'assistant', message.timestamp);
                setWaitingState(false);
                saveState();
                break;
                
            case 'error':
                addErrorMessage(message.message);
                setWaitingState(false);
                break;
                
            case 'typingStart':
                showTypingIndicator();
                break;
                
            case 'typingEnd':
                hideTypingIndicator();
                break;
                
            case 'clearChat':
                clearMessages();
                break;
                
            case 'contextInfo':
                displayContextInfo(message.data);
                break;
                
            case 'codeExecuted':
            case 'codeInserted':
                showSuccessMessage(message.message);
                break;

            case 'processingStep':
                showProcessingStep(message.step);
                break;
        }
    }

    function addMessage(content, role, timestamp) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}`;
        
        const headerDiv = document.createElement('div');
        headerDiv.className = 'message-header';
        headerDiv.innerHTML = `
            <span>${role === 'user' ? 'YOU' : 'MORPHEUS'}</span>
            <span>${formatTimestamp(timestamp)}</span>
        `;
        
        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        
        if (role === 'assistant') {
            contentDiv.innerHTML = formatAssistantMessage(content);
        } else {
            contentDiv.textContent = content;
        }
        
        messageDiv.appendChild(headerDiv);
        messageDiv.appendChild(contentDiv);
        messagesContainer.appendChild(messageDiv);
        
        scrollToBottom();
    }

    function formatAssistantMessage(content) {
        // Convert markdown-style code blocks to HTML
        let formatted = content.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, language, code) => {
            const lang = language || 'text';
            return createCodeBlock(code.trim(), lang);
        });
        
        // Convert inline code
        formatted = formatted.replace(/`([^`]+)`/g, '<code>$1</code>');
        
        // Convert line breaks
        formatted = formatted.replace(/\n/g, '<br>');
        
        return formatted;
    }

    function createCodeBlock(code, language) {
        const codeId = 'code_' + Math.random().toString(36).substr(2, 9);
        return `
            <div class="code-block">
                <div class="code-header">
                    <span>${language}</span>
                    <div class="code-actions">
                        <button data-action="copy" data-code-id="${codeId}" title="Copy">📋</button>
                        <button data-action="execute" data-code-id="${codeId}" data-language="${language}" title="Execute">▶️</button>
                        <button data-action="insert" data-code-id="${codeId}" title="Insert into Editor">📝</button>
                    </div>
                </div>
                <pre id="${codeId}"><code>${escapeHtml(code)}</code></pre>
            </div>
        `;
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function addErrorMessage(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.textContent = message;
        messagesContainer.appendChild(errorDiv);
        scrollToBottom();
    }

    function showSuccessMessage(message) {
        const successDiv = document.createElement('div');
        successDiv.className = 'success-message';
        successDiv.textContent = message;
        messagesContainer.appendChild(successDiv);
        scrollToBottom();
        
        // Remove success message after 3 seconds
        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.parentNode.removeChild(successDiv);
            }
        }, 3000);
    }

    function showTypingIndicator() {
        typingIndicator.style.display = 'flex';
        scrollToBottom();
    }

    function hideTypingIndicator() {
        typingIndicator.style.display = 'none';
    }

    function setWaitingState(waiting) {
        isWaitingForResponse = waiting;
        sendBtn.disabled = waiting;
        messageInput.disabled = waiting;
        
        if (waiting) {
            sendBtn.textContent = '...';
        } else {
            sendBtn.textContent = 'Send';
            messageInput.focus();
        }
    }

    function autoResizeTextarea() {
        messageInput.style.height = 'auto';
        messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';
    }

    function scrollToBottom() {
        setTimeout(() => {
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }, 100);
    }

    function formatTimestamp(timestamp) {
        if (!timestamp) return '';
        const date = new Date(timestamp);
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    function showProcessingStep(step) {
        // Find or create the processing steps container
        let stepsContainer = document.getElementById('processing-steps');
        if (!stepsContainer) {
            stepsContainer = document.createElement('div');
            stepsContainer.id = 'processing-steps';
            stepsContainer.className = 'processing-steps';
            messagesContainer.appendChild(stepsContainer);
        }

        // Create step element
        const stepDiv = document.createElement('div');
        stepDiv.className = 'processing-step';
        stepDiv.textContent = step;

        // Add step to container
        stepsContainer.appendChild(stepDiv);

        // Auto-scroll to show the latest step
        scrollToBottom();

        // Remove steps container when AI response is complete
        setTimeout(() => {
            if (stepsContainer && stepsContainer.parentNode) {
                stepsContainer.remove();
            }
        }, 1000);
    }

    function clearChat() {
        if (confirm('Are you sure you want to clear the chat history?')) {
            vscode.postMessage({ type: 'clearChat' });
        }
    }

    function clearMessages() {
        messagesContainer.innerHTML = '';
        vscode.setState({ messages: [] });
    }

    function exportChat() {
        vscode.postMessage({ type: 'exportChat' });
    }

    function getContext() {
        vscode.postMessage({ type: 'getContext' });
    }

    function handleCodeBlockActions(event) {
        const button = event.target.closest('button[data-action]');
        if (!button) return;

        const action = button.dataset.action;
        const codeId = button.dataset.codeId;
        const language = button.dataset.language;

        switch (action) {
            case 'copy':
                copyCodeHandler(codeId);
                break;
            case 'execute':
                executeCodeHandler(codeId, language);
                break;
            case 'insert':
                insertCodeHandler(codeId);
                break;
        }
    }

    function displayContextInfo(data) {
        const contextDiv = document.createElement('div');
        contextDiv.className = 'context-info';
        
        let symbolsList = '';
        if (data.symbols && data.symbols.length > 0) {
            symbolsList = '<ul>' + data.symbols.map(symbol => 
                `<li>${symbol.name} <span class="symbol-type">${symbol.type} (line ${symbol.line})</span></li>`
            ).join('') + '</ul>';
        } else {
            symbolsList = '<p>No symbols found in current file.</p>';
        }
        
        contextDiv.innerHTML = `
            <h4>Current Context</h4>
            <p><strong>File:</strong> ${data.fileName}</p>
            <p><strong>Language:</strong> ${data.language}</p>
            <h4>Symbols:</h4>
            ${symbolsList}
        `;
        
        messagesContainer.appendChild(contextDiv);
        scrollToBottom();
    }

    function saveState() {
        const messages = Array.from(messagesContainer.children)
            .filter(el => el.classList.contains('message'))
            .map(el => ({
                content: el.querySelector('.message-content').textContent,
                role: el.classList.contains('user') ? 'user' : 'assistant',
                timestamp: el.querySelector('.message-header span:last-child').textContent
            }));
        
        vscode.setState({ messages });
    }

    // Global functions for code block actions (moved to avoid CSP issues)
    function copyCodeHandler(codeId) {
        const codeElement = document.getElementById(codeId);
        if (codeElement) {
            const code = codeElement.textContent;
            navigator.clipboard.writeText(code).then(() => {
                showSuccessMessage('Code copied to clipboard');
            }).catch(() => {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = code;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showSuccessMessage('Code copied to clipboard');
            });
        }
    };

    function executeCodeHandler(codeId, language) {
        const codeElement = document.getElementById(codeId);
        if (codeElement) {
            const code = codeElement.textContent;
            vscode.postMessage({
                type: 'executeCode',
                code: code,
                language: language
            });
        }
    }

    function insertCodeHandler(codeId) {
        const codeElement = document.getElementById(codeId);
        if (codeElement) {
            const code = codeElement.textContent;
            vscode.postMessage({
                type: 'insertCode',
                code: code
            });
        }
    }
})();
