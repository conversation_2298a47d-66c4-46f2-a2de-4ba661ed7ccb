import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import ignore from 'ignore';

export class SecurityManager {
    private context: vscode.ExtensionContext;
    private ignoreFilter: ReturnType<typeof ignore> | null = null;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.loadIgnorePatterns();
    }

    // API Key Management using VS Code's SecretStorage
    public async storeApiKey(key: string): Promise<void> {
        try {
            await this.context.secrets.store('morpheus.apiKey', key);
            vscode.window.showInformationMessage('API key stored securely');
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to store API key: ${error}`);
            throw error;
        }
    }

    public async getApiKey(): Promise<string | undefined> {
        try {
            return await this.context.secrets.get('morpheus.apiKey');
        } catch (error) {
            console.error('Failed to retrieve API key:', error);
            return undefined;
        }
    }

    public async deleteApiKey(): Promise<void> {
        try {
            await this.context.secrets.delete('morpheus.apiKey');
            vscode.window.showInformationMessage('API key deleted');
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to delete API key: ${error}`);
            throw error;
        }
    }

    public async promptForApiKey(): Promise<string | undefined> {
        const apiKey = await vscode.window.showInputBox({
            prompt: 'Enter your xAI Grok API key',
            password: true,
            placeHolder: 'xai-...',
            validateInput: (value) => {
                if (!value || value.trim().length === 0) {
                    return 'API key cannot be empty';
                }
                if (!value.startsWith('xai-')) {
                    return 'Invalid API key format. Should start with "xai-"';
                }
                return null;
            }
        });

        if (apiKey) {
            await this.storeApiKey(apiKey);
            return apiKey;
        }
        return undefined;
    }

    // .morpheusignore file handling
    private loadIgnorePatterns(): void {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            return;
        }

        const ignorePath = path.join(workspaceFolder.uri.fsPath, '.morpheusignore');
        
        try {
            if (fs.existsSync(ignorePath)) {
                const ignoreContent = fs.readFileSync(ignorePath, 'utf8');
                this.ignoreFilter = ignore().add(ignoreContent);
            } else {
                // Create default .morpheusignore if it doesn't exist
                this.createDefaultIgnoreFile(ignorePath);
            }
        } catch (error) {
            console.error('Failed to load .morpheusignore:', error);
            this.ignoreFilter = ignore().add(this.getDefaultIgnorePatterns());
        }
    }

    private createDefaultIgnoreFile(ignorePath: string): void {
        const defaultPatterns = this.getDefaultIgnorePatterns();
        try {
            fs.writeFileSync(ignorePath, defaultPatterns.join('\n'));
            this.ignoreFilter = ignore().add(defaultPatterns);
        } catch (error) {
            console.error('Failed to create .morpheusignore:', error);
            this.ignoreFilter = ignore().add(defaultPatterns);
        }
    }

    private getDefaultIgnorePatterns(): string[] {
        return [
            'node_modules/**',
            '.git/**',
            '*.log',
            '*.tmp',
            '.env*',
            '*.key',
            '*.pem',
            '*.p12',
            '*.pfx',
            '*secret*',
            '*password*',
            '*token*',
            'credentials/**',
            'api-keys/**',
            '__pycache__/**',
            '*.pyc',
            'dist/**',
            'build/**',
            'coverage/**',
            '.vscode/**',
            '.idea/**'
        ];
    }

    public isFileIgnored(filePath: string): boolean {
        if (!this.ignoreFilter) {
            return false;
        }

        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            return false;
        }

        try {
            const relativePath = path.relative(workspaceFolder.uri.fsPath, filePath);

            // Handle empty path case (when filePath is the workspace root)
            if (!relativePath || relativePath === '.') {
                return false;
            }

            // Normalize path separators for cross-platform compatibility
            const normalizedPath = relativePath.replace(/\\/g, '/');

            return this.ignoreFilter.ignores(normalizedPath);
        } catch (error) {
            console.warn(`Error checking if file is ignored: ${filePath}`, error);
            return false; // Default to not ignored if there's an error
        }
    }

    public refreshIgnorePatterns(): void {
        this.loadIgnorePatterns();
    }

    // Content sanitization
    public sanitizeContent(content: string): string {
        // Remove potential API keys, tokens, and sensitive data
        const sensitivePatterns = [
            /\b[A-Za-z0-9]{32,}\b/g, // Generic long alphanumeric strings
            /\bxai-[A-Za-z0-9-_]+/g, // xAI API keys
            /\bsk-[A-Za-z0-9-_]+/g, // OpenAI-style keys
            /\b[A-Za-z0-9+/]{40,}={0,2}\b/g, // Base64 encoded strings
            /\bAKIA[A-Z0-9]{16}\b/g, // AWS Access Keys
            /\b[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\b/gi, // UUIDs
            /\bghp_[A-Za-z0-9]{36}\b/g, // GitHub Personal Access Tokens
            /\bglpat-[A-Za-z0-9-_]{20}\b/g, // GitLab Personal Access Tokens
        ];

        let sanitized = content;
        for (const pattern of sensitivePatterns) {
            sanitized = sanitized.replace(pattern, '[REDACTED]');
        }

        return sanitized;
    }

    // Validate file safety for processing
    public isFileSafeToProcess(filePath: string): boolean {
        // Check if file is ignored
        if (this.isFileIgnored(filePath)) {
            return false;
        }

        // Check file extension
        const ext = path.extname(filePath).toLowerCase();
        const safeExtensions = ['.py', '.vue', '.ts', '.js', '.json', '.md', '.txt', '.html', '.css'];
        if (!safeExtensions.includes(ext)) {
            return false;
        }

        // Check file size (limit to 1MB)
        try {
            const stats = fs.statSync(filePath);
            if (stats.size > 1024 * 1024) {
                return false;
            }
        } catch (error) {
            return false;
        }

        return true;
    }

    // Secure temporary file creation
    public createSecureTempFile(content: string, extension: string = '.tmp'): string {
        const tempDir = this.context.globalStorageUri.fsPath;
        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
        }

        const tempFileName = `morpheus_${Date.now()}_${Math.random().toString(36).substr(2, 9)}${extension}`;
        const tempFilePath = path.join(tempDir, tempFileName);
        
        fs.writeFileSync(tempFilePath, content, { mode: 0o600 }); // Read/write for owner only
        return tempFilePath;
    }

    // Clean up temporary files
    public cleanupTempFiles(): void {
        const tempDir = this.context.globalStorageUri.fsPath;
        if (!fs.existsSync(tempDir)) {
            return;
        }

        try {
            const files = fs.readdirSync(tempDir);
            const now = Date.now();
            const maxAge = 24 * 60 * 60 * 1000; // 24 hours

            for (const file of files) {
                if (file.startsWith('morpheus_')) {
                    const filePath = path.join(tempDir, file);
                    const stats = fs.statSync(filePath);
                    if (now - stats.mtime.getTime() > maxAge) {
                        fs.unlinkSync(filePath);
                    }
                }
            }
        } catch (error) {
            console.error('Failed to cleanup temp files:', error);
        }
    }

    public dispose(): void {
        this.cleanupTempFiles();
    }
}
