import * as vscode from 'vscode';
import axios, { AxiosResponse } from 'axios';

export interface CompletionOptions {
    maxTokens?: number;
    temperature?: number;
    stopSequences?: string[];
    model?: string;
}

export interface ChatMessage {
    role: 'system' | 'user' | 'assistant';
    content: string;
}

export interface ChatOptions {
    maxTokens?: number;
    temperature?: number;
    model?: string;
    stream?: boolean;
    showSteps?: boolean;
    onStep?: (step: string) => void;
}

export interface ProcessingStep {
    id: string;
    description: string;
    status: 'pending' | 'in-progress' | 'completed' | 'error';
    timestamp: number;
    details?: string;
}

export class AIService {
    private apiKey: string | null = null;
    private baseUrl: string;
    private cache: Map<string, { response: string; timestamp: number }> = new Map();
    private readonly cacheExpirationMs = 60 * 60 * 1000; // 1 hour

    constructor() {
        this.baseUrl = vscode.workspace.getConfiguration('morpheus').get('apiEndpoint', 'https://api.x.ai/v1');
        this.loadApiKey();
    }

    private async loadApiKey(): Promise<void> {
        try {
            // First, try to get API key from VS Code configuration
            const configApiKey = vscode.workspace.getConfiguration('morpheus').get<string>('apiKey');
            if (configApiKey && configApiKey.trim().length > 0) {
                this.apiKey = configApiKey.trim();
                console.log('✅ API key loaded from configuration');
                return;
            }

            // If not in config, try secrets store
            const context = (global as any).morpheusContext;
            if (context?.secrets) {
                const secretApiKey = await context.secrets.get('morpheus.apiKey');
                if (secretApiKey && secretApiKey.trim().length > 0) {
                    this.apiKey = secretApiKey.trim();
                    console.log('✅ API key loaded from secrets store');
                    return;
                }
            }

            console.log('⚠️ No API key found in configuration or secrets store');
        } catch (error) {
            console.error('Failed to load API key:', error);
        }
    }

    public async setApiKey(apiKey: string): Promise<void> {
        this.apiKey = apiKey;
        try {
            // Save to VS Code configuration (primary storage)
            await vscode.workspace.getConfiguration('morpheus').update('apiKey', apiKey, vscode.ConfigurationTarget.Global);
            console.log('✅ API key saved to configuration');

            // Also save to secrets store as backup
            const context = (global as any).morpheusContext;
            if (context?.secrets) {
                await context.secrets.store('morpheus.apiKey', apiKey);
                console.log('✅ API key saved to secrets store');
            }
        } catch (error) {
            console.error('Failed to store API key:', error);
        }
    }

    public async generateCompletion(prompt: string, options: CompletionOptions = {}): Promise<string | null> {
        // Always try to reload API key before making requests
        if (!this.apiKey) {
            await this.loadApiKey();
        }

        if (!this.apiKey) {
            await this.promptForApiKey();
            if (!this.apiKey) {
                return null;
            }
        }

        const cacheKey = this.generateCacheKey('completion', prompt, options);
        const cached = this.getCachedResponse(cacheKey);
        if (cached) {
            return cached;
        }

        try {
            const response = await this.makeCompletionRequest(prompt, options);
            const completion = this.extractCompletionFromResponse(response);
            
            if (completion) {
                this.cacheResponse(cacheKey, completion);
            }
            
            return completion;
        } catch (error) {
            console.error('Completion request failed:', error);
            this.handleApiError(error);
            return null;
        }
    }

    public async generateChatResponse(messages: ChatMessage[], options: ChatOptions = {}): Promise<string | null> {
        const steps: ProcessingStep[] = [];
        const reportStep = (description: string, status: 'pending' | 'in-progress' | 'completed' | 'error' = 'in-progress', details?: string) => {
            const step: ProcessingStep = {
                id: Date.now().toString(),
                description,
                status,
                timestamp: Date.now(),
                details
            };
            steps.push(step);
            if (options.onStep) {
                options.onStep(`${status === 'completed' ? '✅' : status === 'error' ? '❌' : '🔄'} ${description}${details ? `: ${details}` : ''}`);
            }
        };

        if (options.showSteps) {
            reportStep('Initializing Morpheus AI system', 'in-progress');
        }

        // Always try to reload API key before making requests
        if (!this.apiKey) {
            if (options.showSteps) reportStep('Loading API credentials', 'in-progress');
            await this.loadApiKey();
        }

        if (!this.apiKey) {
            if (options.showSteps) reportStep('API key required', 'in-progress');
            await this.promptForApiKey();
            if (!this.apiKey) {
                if (options.showSteps) reportStep('Authentication failed', 'error');
                return null;
            }
        }

        if (options.showSteps) {
            reportStep('Authentication verified', 'completed');
            reportStep('Checking response cache', 'in-progress');
        }

        const cacheKey = this.generateCacheKey('chat', JSON.stringify(messages), options);
        const cached = this.getCachedResponse(cacheKey);
        if (cached) {
            if (options.showSteps) reportStep('Found cached response', 'completed');
            return cached;
        }

        if (options.showSteps) {
            reportStep('No cached response found', 'completed');
            reportStep('Preparing request to Grok-4', 'in-progress');
        }

        try {
            if (options.showSteps) {
                reportStep('Sending request to xAI Grok-4', 'in-progress', `${messages.length} messages`);
            }

            const response = await this.makeChatRequest(messages, options);

            if (options.showSteps) {
                reportStep('Received response from Grok-4', 'completed');
                reportStep('Processing AI response', 'in-progress');
            }

            const chatResponse = this.extractChatResponseFromResponse(response);

            if (chatResponse) {
                if (options.showSteps) {
                    reportStep('Response processed successfully', 'completed');
                    reportStep('Caching response for future use', 'in-progress');
                }
                this.cacheResponse(cacheKey, chatResponse);
                if (options.showSteps) {
                    reportStep('Response cached', 'completed');
                    reportStep('Analysis complete', 'completed');
                }
            } else {
                if (options.showSteps) reportStep('Failed to process response', 'error');
            }

            return chatResponse;
        } catch (error) {
            console.error('Chat request failed:', error);
            if (options.showSteps) reportStep('Request failed', 'error', error instanceof Error ? error.message : 'Unknown error');
            this.handleApiError(error);
            return null;
        }
    }

    private async makeCompletionRequest(prompt: string, options: CompletionOptions): Promise<AxiosResponse> {
        const requestBody = {
            model: options.model || vscode.workspace.getConfiguration('morpheus').get('model', 'grok-4-latest'),
            prompt: prompt,
            max_tokens: options.maxTokens || 1000,
            temperature: options.temperature || 0.3,
            stop: options.stopSequences || [],
            stream: false
        };

        return await axios.post(
            `${this.baseUrl}/completions`,
            requestBody,
            {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                timeout: 30000 // 30 second timeout
            }
        );
    }

    private async makeChatRequest(messages: ChatMessage[], options: ChatOptions): Promise<AxiosResponse> {
        const configModel = vscode.workspace.getConfiguration('morpheus').get('model', 'grok-4-latest');
        const finalModel = options.model || configModel;

        console.log('Config model:', configModel);
        console.log('Options model:', options.model);
        console.log('Final model:', finalModel);

        const requestBody = {
            model: finalModel,
            messages: messages,
            max_tokens: options.maxTokens || 2000,
            temperature: options.temperature || 0.7,
            stream: options.stream || false
        };

        const url = `${this.baseUrl}/chat/completions`;

        console.log('Making request to:', url);
        console.log('Request body:', JSON.stringify(requestBody, null, 2));
        console.log('API Key present:', !!this.apiKey);
        console.log('API Key prefix:', this.apiKey?.substring(0, 8) + '...');

        return await axios.post(
            url,
            requestBody,
            {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                timeout: 120000 // 120 second timeout for chat (increased for large file generation)
            }
        );
    }

    private extractCompletionFromResponse(response: AxiosResponse): string | null {
        try {
            const data = response.data;
            if (data.choices && data.choices.length > 0) {
                return data.choices[0].text || data.choices[0].message?.content || null;
            }
        } catch (error) {
            console.error('Failed to extract completion from response:', error);
        }
        return null;
    }

    private extractChatResponseFromResponse(response: AxiosResponse): string | null {
        try {
            const data = response.data;
            if (data.choices && data.choices.length > 0) {
                const choice = data.choices[0];
                return choice.message?.content || choice.text || null;
            }
        } catch (error) {
            console.error('Failed to extract chat response from response:', error);
        }
        return null;
    }

    private async promptForApiKey(): Promise<void> {
        const apiKey = await vscode.window.showInputBox({
            prompt: 'Enter your xAI Grok API key',
            password: true,
            placeHolder: 'xai-...',
            validateInput: (value) => {
                if (!value || value.trim().length === 0) {
                    return 'API key cannot be empty';
                }
                if (!value.startsWith('xai-')) {
                    return 'Invalid API key format. Should start with "xai-"';
                }
                return null;
            }
        });

        if (apiKey) {
            await this.setApiKey(apiKey);
            vscode.window.showInformationMessage('API key saved successfully');
        }
    }

    private handleApiError(error: any): void {
        console.error('Full API Error:', error);

        if (error.response) {
            const status = error.response.status;
            const responseData = error.response.data;
            const message = responseData?.error?.message || responseData?.message || 'Unknown API error';

            console.error('API Response Status:', status);
            console.error('API Response Data:', responseData);

            switch (status) {
                case 401:
                    vscode.window.showErrorMessage(`Authentication failed: ${message}. Please check your xAI API key.`);
                    this.apiKey = null; // Clear invalid key
                    break;
                case 404:
                    vscode.window.showErrorMessage(`API endpoint not found: ${message}. The xAI API structure may have changed.`);
                    break;
                case 429:
                    vscode.window.showWarningMessage(`Rate limit exceeded: ${message}. Please try again later.`);
                    break;
                case 500:
                    vscode.window.showErrorMessage(`xAI service error: ${message}. Please try again later.`);
                    break;
                default:
                    vscode.window.showErrorMessage(`API error (${status}): ${message}`);
            }
        } else if (error.code === 'ECONNABORTED') {
            vscode.window.showErrorMessage('Request timeout. Please check your internet connection.');
        } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
            vscode.window.showErrorMessage('Cannot connect to xAI API. Please check your internet connection.');
        } else {
            vscode.window.showErrorMessage(`Network error: ${error.message}`);
        }
    }

    // Caching methods
    private generateCacheKey(type: string, content: string, options: any): string {
        const optionsStr = JSON.stringify(options);
        const contentHash = this.simpleHash(content + optionsStr);
        return `${type}:${contentHash}`;
    }

    private simpleHash(str: string): string {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash.toString(36);
    }

    private getCachedResponse(key: string): string | null {
        const config = vscode.workspace.getConfiguration('morpheus');
        if (!config.get('cacheResponses', true)) {
            return null;
        }

        const cached = this.cache.get(key);
        if (cached && Date.now() - cached.timestamp < this.cacheExpirationMs) {
            return cached.response;
        }
        
        if (cached) {
            this.cache.delete(key);
        }
        
        return null;
    }

    private cacheResponse(key: string, response: string): void {
        const config = vscode.workspace.getConfiguration('morpheus');
        if (!config.get('cacheResponses', true)) {
            return;
        }

        this.cache.set(key, {
            response,
            timestamp: Date.now()
        });

        // Clean up old cache entries
        if (this.cache.size > 200) {
            this.cleanupCache();
        }
    }

    private cleanupCache(): void {
        const now = Date.now();
        for (const [key, value] of this.cache.entries()) {
            if (now - value.timestamp > this.cacheExpirationMs) {
                this.cache.delete(key);
            }
        }
    }

    // Cost tracking
    private totalTokensUsed = 0;
    private totalRequests = 0;

    public getUsageStats(): { totalTokens: number; totalRequests: number; estimatedCost: number } {
        // Rough cost estimation (adjust based on actual xAI pricing)
        const costPerToken = 0.00001; // $0.01 per 1K tokens
        const estimatedCost = this.totalTokensUsed * costPerToken;
        
        return {
            totalTokens: this.totalTokensUsed,
            totalRequests: this.totalRequests,
            estimatedCost
        };
    }

    public resetUsageStats(): void {
        this.totalTokensUsed = 0;
        this.totalRequests = 0;
    }

    // Test API connection
    public async testConnection(): Promise<boolean> {
        if (!this.apiKey) {
            return false;
        }

        try {
            const response = await this.generateCompletion('Test', { maxTokens: 1 });
            return response !== null;
        } catch (error) {
            return false;
        }
    }

    public clearCache(): void {
        this.cache.clear();
    }
}
