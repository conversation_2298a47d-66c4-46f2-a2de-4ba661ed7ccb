import * as vscode from 'vscode';

export class ConfigurationManager {
    private context: vscode.ExtensionContext;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
    }

    public getConfiguration(): vscode.WorkspaceConfiguration {
        return vscode.workspace.getConfiguration('morpheus');
    }

    public get<T>(key: string, defaultValue?: T): T {
        return this.getConfiguration().get<T>(key, defaultValue as T);
    }

    public async update(key: string, value: any, configurationTarget?: vscode.ConfigurationTarget): Promise<void> {
        await this.getConfiguration().update(key, value, configurationTarget);
    }

    // Specific configuration getters
    public isInlineCompletionsEnabled(): boolean {
        return this.get<boolean>('enableInlineCompletions', true);
    }

    public isContextEngineEnabled(): boolean {
        return this.get<boolean>('enableContextEngine', true);
    }

    public getMaxContextTokens(): number {
        return this.get<number>('maxContextTokens', 200000);
    }

    public isCacheEnabled(): boolean {
        return this.get<boolean>('cacheResponses', true);
    }

    public getOutputDirectory(): string {
        return this.get<string>('outputDirectory', './morpheus-output');
    }

    public getExcludePatterns(): string[] {
        return this.get<string[]>('excludePatterns', [
            'node_modules/**',
            '.git/**',
            '*.log',
            '*.tmp'
        ]);
    }

    public getSupportedLanguages(): string[] {
        return this.get<string[]>('supportedLanguages', [
            'python',
            'vue',
            'typescript',
            'javascript'
        ]);
    }

    public getApiEndpoint(): string {
        return this.get<string>('apiEndpoint', 'https://api.x.ai/v1');
    }

    public getModel(): string {
        return this.get<string>('model', 'grok-3-latest');
    }

    public getCompletionMaxTokens(): number {
        return this.get<number>('completionMaxTokens', 1000);
    }

    public getCompletionTemperature(): number {
        return this.get<number>('completionTemperature', 0.3);
    }

    public getCacheExpirationHours(): number {
        return this.get<number>('cacheExpirationHours', 24);
    }

    public isAgentModeEnabled(): boolean {
        return this.get<boolean>('enableAgentMode', true);
    }

    public getAgentConfirmationRequired(): boolean {
        return this.get<boolean>('agentConfirmationRequired', true);
    }

    public getMaxAgentSteps(): number {
        return this.get<number>('maxAgentSteps', 10);
    }

    // Workspace-specific settings
    public getWorkspaceSpecificSetting<T>(key: string, defaultValue: T): T {
        const workspaceConfig = vscode.workspace.getConfiguration('morpheus', vscode.workspace.workspaceFolders?.[0]);
        return workspaceConfig.get<T>(key, defaultValue);
    }

    public async updateWorkspaceSpecificSetting(key: string, value: any): Promise<void> {
        const workspaceConfig = vscode.workspace.getConfiguration('morpheus', vscode.workspace.workspaceFolders?.[0]);
        await workspaceConfig.update(key, value, vscode.ConfigurationTarget.Workspace);
    }

    // Validation methods
    public validateConfiguration(): { isValid: boolean; errors: string[] } {
        const errors: string[] = [];
        
        const maxTokens = this.getMaxContextTokens();
        if (maxTokens < 1000 || maxTokens > 500000) {
            errors.push('maxContextTokens must be between 1000 and 500000');
        }

        const supportedLanguages = this.getSupportedLanguages();
        const validLanguages = ['python', 'vue', 'typescript', 'javascript', 'html', 'css', 'json'];
        for (const lang of supportedLanguages) {
            if (!validLanguages.includes(lang)) {
                errors.push(`Unsupported language: ${lang}`);
            }
        }

        const temperature = this.getCompletionTemperature();
        if (temperature < 0 || temperature > 2) {
            errors.push('completionTemperature must be between 0 and 2');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    // Migration methods for configuration updates
    public async migrateConfiguration(): Promise<void> {
        const config = this.getConfiguration();
        const currentVersion = this.context.globalState.get('morpheus.configVersion', '1.0.0');
        
        // Add migration logic here for future versions
        if (currentVersion === '1.0.0') {
            // Example migration: rename old setting to new setting
            // const oldValue = config.get('oldSettingName');
            // if (oldValue !== undefined) {
            //     await config.update('newSettingName', oldValue);
            //     await config.update('oldSettingName', undefined);
            // }
        }

        await this.context.globalState.update('morpheus.configVersion', '1.0.0');
    }

    // Reset configuration to defaults
    public async resetToDefaults(): Promise<void> {
        const config = this.getConfiguration();
        const inspect = config.inspect('');
        
        if (inspect) {
            for (const key of Object.keys(inspect)) {
                await config.update(key, undefined, vscode.ConfigurationTarget.Global);
                await config.update(key, undefined, vscode.ConfigurationTarget.Workspace);
            }
        }
    }
}
