# Morpheus VS Code Extension - Git Ignore File

# Compiled output
dist/
out/
*.vsix

# Node.js dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Temporary folders
tmp/
temp/

# Logs
logs
*.log

# VS Code specific
.vscode/settings.json
.vscode/launch.json
.vscode/extensions.json
.vscode/*.code-snippets

# Keep these VS Code files for development
!.vscode/tasks.json

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Windows
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~

# IDE files
.idea/
*.swp
*.swo
*~

# Extension development
*.vsix
vsc-extension-quickstart.md

# Test files and coverage
test-results/
coverage/
.nyc_output/

# Build artifacts
lib/
build/

# Webpack
.webpack/

# Generated files
generated_script.py
morpheus_*.tmp
*.generated.*

# API keys and secrets (extra safety)
*.key
*.pem
*.p12
*.pfx
*secret*
*password*
*token*
api-keys/
credentials/

# Database files
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup
*.old

# Archive files
*.zip
*.tar.gz
*.rar
*.7z

# Package files
*.deb
*.rpm
*.pkg
*.dmg

# Morpheus specific temporary files
morpheus-output/
.morpheus-cache/
morpheus_temp_*

# Extension packaging
*.vsix
package/

# Local configuration overrides
.vscode/settings.json
morpheus.local.json

# Development utilities
scripts/test-extension-output.txt
debug.log
error.log
