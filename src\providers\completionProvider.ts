import * as vscode from 'vscode';
import { ContextEngine } from '../core/contextEngine';
import { AIService } from '../services/aiService';

export class CompletionProvider implements vscode.InlineCompletionItemProvider {
    private contextEngine: ContextEngine;
    private aiService: AIService;
    private cache: Map<string, { completion: string; timestamp: number }> = new Map();
    private readonly cacheExpirationMs = 5 * 60 * 1000; // 5 minutes

    constructor(contextEngine: ContextEngine) {
        this.contextEngine = contextEngine;
        this.aiService = new AIService();
    }

    public async provideInlineCompletionItems(
        document: vscode.TextDocument,
        position: vscode.Position,
        context: vscode.InlineCompletionContext,
        token: vscode.CancellationToken
    ): Promise<vscode.InlineCompletionItem[] | vscode.InlineCompletionList | null> {
        
        // Check if inline completions are enabled
        const config = vscode.workspace.getConfiguration('morpheus');
        if (!config.get('enableInlineCompletions', true)) {
            return null;
        }

        // Skip if triggered by user typing too fast
        if (context.triggerKind === vscode.InlineCompletionTriggerKind.Automatic) {
            const now = Date.now();
            const lastTrigger = this.getLastTriggerTime(document.uri.toString());
            if (now - lastTrigger < 500) { // Debounce 500ms
                return null;
            }
            this.setLastTriggerTime(document.uri.toString(), now);
        }

        try {
            const completion = await this.generateCompletion(document, position, token);
            if (!completion || completion.trim().length === 0) {
                return null;
            }

            return [
                new vscode.InlineCompletionItem(
                    completion,
                    new vscode.Range(position, position)
                )
            ];
        } catch (error) {
            console.error('Error generating completion:', error);
            return null;
        }
    }

    private async generateCompletion(
        document: vscode.TextDocument,
        position: vscode.Position,
        token: vscode.CancellationToken
    ): Promise<string | null> {
        
        // Get context around cursor
        const linePrefix = document.lineAt(position).text.substring(0, position.character);
        const lineSuffix = document.lineAt(position).text.substring(position.character);
        
        // Skip if we're in the middle of a word
        if (lineSuffix.match(/^\w/)) {
            return null;
        }

        // Generate cache key (reduced context for speed)
        const contextLines = this.getContextLines(document, position, 5);
        const cacheKey = this.generateCacheKey(document.uri.toString(), position, contextLines);
        
        // Check cache first
        const cached = this.getCachedCompletion(cacheKey);
        if (cached) {
            return cached;
        }

        // Get enhanced context from the context engine (reduced for speed)
        const relevantContext = this.contextEngine.getRelevantContext(
            document.uri.fsPath,
            2000 // Significantly reduced context for faster completions
        );

        // Get symbols for better completion context
        const currentFileSymbols = this.contextEngine.getSymbolsInFile(document.uri.fsPath);
        const workspaceStructure = this.contextEngine.getWorkspaceStructure();

        // Analyze what the user is trying to complete
        const completionContext = this.analyzeCompletionContext(document, position, linePrefix);

        // Get relevant symbols for this completion
        const relevantSymbols = this.getRelevantSymbolsForCompletion(
            document.uri.fsPath,
            completionContext,
            currentFileSymbols
        );

        // Prepare the enhanced prompt
        const prompt = this.buildEnhancedCompletionPrompt(
            document,
            position,
            contextLines,
            relevantContext,
            linePrefix,
            lineSuffix,
            relevantSymbols,
            workspaceStructure,
            completionContext
        );

        // Generate completion using AI service with optimized settings
        const completion = await this.aiService.generateCompletion(prompt, {
            maxTokens: 50,  // Reduced for faster responses
            temperature: 0.1,  // Lower temperature for more predictable, faster responses
            stopSequences: ['\n\n', '```', '\n}', '\n)', ';']  // More stop sequences for faster completion
        });

        if (completion && completion.trim().length > 0) {
            this.cacheCompletion(cacheKey, completion);
            return this.postProcessCompletion(completion, linePrefix);
        }

        return null;
    }

    private getContextLines(document: vscode.TextDocument, position: vscode.Position, lineCount: number): string {
        const startLine = Math.max(0, position.line - lineCount);
        const endLine = Math.min(document.lineCount - 1, position.line + lineCount);
        
        const lines: string[] = [];
        for (let i = startLine; i <= endLine; i++) {
            lines.push(document.lineAt(i).text);
        }
        
        return lines.join('\n');
    }

    private buildCompletionPrompt(
        document: vscode.TextDocument,
        position: vscode.Position,
        contextLines: string,
        relevantContext: string,
        linePrefix: string,
        lineSuffix: string
    ): string {
        const language = document.languageId;
        const fileName = document.fileName;
        
        return `You are an AI code completion assistant. Generate a code completion for the cursor position.

Language: ${language}
File: ${fileName}

Relevant codebase context:
${relevantContext.substring(0, 10000)} // Limit context to prevent token overflow

Current file context:
${contextLines}

Current line:
${linePrefix}<CURSOR>${lineSuffix}

Instructions:
1. Generate only the code that should be inserted at the cursor position
2. Do not repeat the existing line prefix
3. Focus on completing the current statement or expression
4. Consider the coding patterns and style from the codebase context
5. For Python: Follow PEP 8 conventions
6. For Vue: Follow Vue.js best practices
7. For TypeScript/JavaScript: Follow modern ES6+ patterns
8. Keep completions concise and relevant
9. Do not include explanations or comments unless they're part of the code

Completion:`;
    }

    private postProcessCompletion(completion: string, linePrefix: string): string {
        // Remove any leading whitespace that would duplicate existing indentation
        let processed = completion.trimStart();

        // Clean up any markdown formatting that might have slipped through
        processed = processed.replace(/^```[\w]*\n?/gm, '');
        processed = processed.replace(/\n?```$/gm, '');
        processed = processed.replace(/```/g, '');

        // Remove any duplicate content that might already exist in the line prefix
        const words = linePrefix.trim().split(/\s+/);
        if (words.length > 0) {
            const lastWord = words[words.length - 1];
            if (processed.startsWith(lastWord)) {
                processed = processed.substring(lastWord.length);
            }
        }

        // Ensure proper indentation
        if (linePrefix.match(/^\s+/) && !processed.startsWith(' ')) {
            const indentation = linePrefix.match(/^\s+/)?.[0] || '';
            if (processed.includes('\n')) {
                processed = processed.replace(/\n/g, '\n' + indentation);
            }
        }

        return processed;
    }

    private generateCacheKey(uri: string, position: vscode.Position, context: string): string {
        const contextHash = this.simpleHash(context);
        return `${uri}:${position.line}:${position.character}:${contextHash}`;
    }

    private simpleHash(str: string): string {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash.toString(36);
    }

    private getCachedCompletion(key: string): string | null {
        const cached = this.cache.get(key);
        if (cached && Date.now() - cached.timestamp < this.cacheExpirationMs) {
            return cached.completion;
        }
        if (cached) {
            this.cache.delete(key);
        }
        return null;
    }

    private cacheCompletion(key: string, completion: string): void {
        this.cache.set(key, {
            completion,
            timestamp: Date.now()
        });

        // Clean up old cache entries
        if (this.cache.size > 100) {
            this.cleanupCache();
        }
    }

    private cleanupCache(): void {
        const now = Date.now();
        for (const [key, value] of this.cache.entries()) {
            if (now - value.timestamp > this.cacheExpirationMs) {
                this.cache.delete(key);
            }
        }
    }

    // Trigger time tracking for debouncing
    private triggerTimes: Map<string, number> = new Map();

    private getLastTriggerTime(uri: string): number {
        return this.triggerTimes.get(uri) || 0;
    }

    private setLastTriggerTime(uri: string, time: number): void {
        this.triggerTimes.set(uri, time);
    }

    public updateConfiguration(): void {
        // Clear cache when configuration changes
        this.cache.clear();
        this.triggerTimes.clear();
    }

    // Pattern matching for business logic completions
    private detectBusinessPatterns(context: string, linePrefix: string): string | null {
        // Detect common business patterns and suggest appropriate completions
        
        // Database query patterns
        if (linePrefix.includes('SELECT') || linePrefix.includes('select')) {
            return this.suggestSQLCompletion(context, linePrefix);
        }

        // Report generation patterns
        if (linePrefix.includes('report') || linePrefix.includes('generate')) {
            return this.suggestReportCompletion(context, linePrefix);
        }

        // Data processing patterns
        if (linePrefix.includes('process') || linePrefix.includes('transform')) {
            return this.suggestDataProcessingCompletion(context, linePrefix);
        }

        return null;
    }

    private suggestSQLCompletion(context: string, linePrefix: string): string | null {
        // Analyze context for table names and suggest appropriate SQL completions
        const tableMatches = context.match(/FROM\s+(\w+)/gi);
        if (tableMatches && tableMatches.length > 0) {
            const tableName = tableMatches[0].split(/\s+/)[1];
            if (linePrefix.endsWith('SELECT ')) {
                return `* FROM ${tableName} WHERE `;
            }
        }
        return null;
    }

    private suggestReportCompletion(context: string, linePrefix: string): string | null {
        // Suggest report generation patterns
        if (linePrefix.includes('def generate_report') || linePrefix.includes('function generateReport')) {
            return `(data, format='csv'):\n    """Generate business report from data"""\n    `;
        }
        return null;
    }

    private suggestDataProcessingCompletion(context: string, linePrefix: string): string | null {
        // Suggest data processing patterns
        if (linePrefix.includes('pandas') || linePrefix.includes('pd.')) {
            return this.suggestPandasCompletion(linePrefix);
        }
        return null;
    }

    private suggestPandasCompletion(linePrefix: string): string | null {
        if (linePrefix.endsWith('pd.read_')) {
            return 'csv(';
        }
        if (linePrefix.endsWith('.groupby(')) {
            return "'column_name').agg(";
        }
        return null;
    }

    private analyzeCompletionContext(document: vscode.TextDocument, position: vscode.Position, linePrefix: string): any {
        const line = document.lineAt(position.line).text;
        const wordRange = document.getWordRangeAtPosition(position);
        const currentWord = wordRange ? document.getText(wordRange) : '';

        return {
            language: document.languageId,
            currentWord,
            linePrefix,
            isInFunction: this.isInFunction(document, position),
            isInClass: this.isInClass(document, position),
            isAfterDot: linePrefix.trim().endsWith('.'),
            isImportStatement: linePrefix.trim().startsWith('import ') || linePrefix.trim().startsWith('from '),
            indentLevel: this.getIndentLevel(line),
            nearbyKeywords: this.getNearbyKeywords(document, position)
        };
    }

    private getRelevantSymbolsForCompletion(filePath: string, completionContext: any, currentFileSymbols: any[]): any[] {
        const relevantSymbols: any[] = [];

        // If completing after a dot, look for methods/properties
        if (completionContext.isAfterDot) {
            const objectName = this.extractObjectName(completionContext.linePrefix);
            if (objectName) {
                // Find symbols related to this object
                const relatedSymbols = this.contextEngine.searchSymbols(objectName);
                relevantSymbols.push(...relatedSymbols.filter(s => s.type === 'method' || s.type === 'property'));
            }
        }

        // If in import statement, get exported symbols
        if (completionContext.isImportStatement) {
            const allSymbols = this.contextEngine.getAllSymbols();
            relevantSymbols.push(...allSymbols.filter(s => s.isExported));
        }

        // Get symbols with matching prefix
        if (completionContext.currentWord) {
            const matchingSymbols = this.contextEngine.getSymbolsForCompletion(filePath, completionContext.currentWord);
            relevantSymbols.push(...matchingSymbols);
        }

        return relevantSymbols.slice(0, 20); // Limit for performance
    }

    private buildEnhancedCompletionPrompt(
        document: vscode.TextDocument,
        position: vscode.Position,
        contextLines: string,
        relevantContext: string,
        linePrefix: string,
        lineSuffix: string,
        relevantSymbols: any[],
        workspaceStructure: any,
        completionContext: any
    ): string {
        const symbolsInfo = relevantSymbols.length > 0
            ? `\n\nAvailable symbols:\n${relevantSymbols.map(s =>
                `- ${s.name} (${s.type}): ${s.docstring || 'No description'}`
              ).join('\n')}`
            : '';

        const projectInfo = workspaceStructure
            ? `\n\nProject type: ${workspaceStructure.moduleType || 'unknown'}`
            : '';

        return `You are an AI code completion assistant. Complete the code at the cursor position.

Language: ${document.languageId}
Context: ${completionContext.isInFunction ? 'inside function' : completionContext.isInClass ? 'inside class' : 'top level'}

Current code context:
\`\`\`${document.languageId}
${contextLines}
\`\`\`

Relevant workspace context:
${relevantContext.substring(0, 2000)} // Truncated for completion

${symbolsInfo}${projectInfo}

Complete the code after: "${linePrefix}"
Before: "${lineSuffix}"

Rules:
1. Only provide the completion text, no explanations
2. Match the existing code style and indentation
3. Use appropriate symbols from the workspace
4. Keep completions concise and relevant
5. Don't repeat the existing code

Completion:`;
    }

    private isInFunction(document: vscode.TextDocument, position: vscode.Position): boolean {
        for (let i = position.line - 1; i >= 0; i--) {
            const line = document.lineAt(i).text.trim();
            if (line.match(/^def\s+\w+/) || line.match(/function\s+\w+/) || line.match(/\w+\s*=\s*function/)) {
                return true;
            }
            if (line.match(/^class\s+\w+/) && document.languageId === 'python') {
                return false;
            }
        }
        return false;
    }

    private isInClass(document: vscode.TextDocument, position: vscode.Position): boolean {
        for (let i = position.line - 1; i >= 0; i--) {
            const line = document.lineAt(i).text.trim();
            if (line.match(/^class\s+\w+/)) {
                return true;
            }
        }
        return false;
    }

    private getIndentLevel(line: string): number {
        const match = line.match(/^(\s*)/);
        return match ? match[1].length : 0;
    }

    private getNearbyKeywords(document: vscode.TextDocument, position: vscode.Position): string[] {
        const keywords: string[] = [];
        const range = 5; // Look 5 lines up and down

        for (let i = Math.max(0, position.line - range); i <= Math.min(document.lineCount - 1, position.line + range); i++) {
            const line = document.lineAt(i).text;
            const matches = line.match(/\b(def|class|import|from|if|for|while|try|except|with|async|await)\b/g);
            if (matches) {
                keywords.push(...matches);
            }
        }

        return [...new Set(keywords)];
    }

    private extractObjectName(linePrefix: string): string | null {
        const match = linePrefix.match(/(\w+)\.$/);
        return match ? match[1] : null;
    }
}
