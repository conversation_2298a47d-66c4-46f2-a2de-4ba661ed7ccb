import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import * as chokidar from 'chokidar';
import { SecurityManager } from './securityManager';

// Simple regex-based parsing (fallback for tree-sitter issues)
// This provides basic symbol extraction without native dependencies

export interface Symbol {
    name: string;
    type: 'function' | 'class' | 'variable' | 'method' | 'property' | 'import' | 'interface' | 'type';
    filePath: string;
    startLine: number;
    endLine: number;
    content: string;
    context?: string;
    parameters?: string[];
    returnType?: string;
    docstring?: string;
    scope?: string;
    isExported?: boolean;
    dependencies?: string[];
}

export interface FileIndex {
    filePath: string;
    lastModified: number;
    symbols: Symbol[];
    content: string;
    language: string;
    imports: string[];
    exports: string[];
    dependencies: string[];
    projectStructure?: ProjectStructure;
}

export interface ProjectStructure {
    packageJson?: any;
    tsConfig?: any;
    pythonRequirements?: string[];
    isMainModule?: boolean;
    moduleType?: 'commonjs' | 'esm' | 'python' | 'vue';
}

export class ContextEngine {
    private static readonly INDEX_VERSION = '2.0.0'; // Increment when schema changes
    private context: vscode.ExtensionContext;
    private securityManager: SecurityManager;
    private fileIndex: Map<string, FileIndex> = new Map();
    private watcher: chokidar.FSWatcher | null = null;
    private isIndexing = false;
    private workspaceStructure: ProjectStructure | null = null;
    private symbolGraph: Map<string, Set<string>> = new Map(); // symbol -> files that reference it

    constructor(context: vscode.ExtensionContext, securityManager: SecurityManager) {
        this.context = context;
        this.securityManager = securityManager;
    }

    public async initialize(): Promise<void> {
        console.log('Initializing Context Engine...');

        // Analyze workspace structure first
        await this.analyzeWorkspaceStructure();

        // Load existing index from storage
        await this.loadIndexFromStorage();

        // Start file watching
        this.startFileWatcher();

        // Perform initial indexing
        await this.performInitialIndexing();

        // Build symbol graph for better context
        this.buildSymbolGraph();

        console.log('Context Engine initialized successfully');
    }

    private async analyzeWorkspaceStructure(): Promise<void> {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            return;
        }

        const rootPath = workspaceFolder.uri.fsPath;
        this.workspaceStructure = {
            moduleType: 'commonjs'
        };

        try {
            // Check for package.json
            const packageJsonPath = path.join(rootPath, 'package.json');
            if (fs.existsSync(packageJsonPath)) {
                const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
                this.workspaceStructure.packageJson = packageJson;
                this.workspaceStructure.moduleType = packageJson.type === 'module' ? 'esm' : 'commonjs';
            }

            // Check for tsconfig.json
            const tsConfigPath = path.join(rootPath, 'tsconfig.json');
            if (fs.existsSync(tsConfigPath)) {
                this.workspaceStructure.tsConfig = JSON.parse(fs.readFileSync(tsConfigPath, 'utf8'));
            }

            // Check for Python requirements
            const requirementsPath = path.join(rootPath, 'requirements.txt');
            if (fs.existsSync(requirementsPath)) {
                this.workspaceStructure.pythonRequirements = fs.readFileSync(requirementsPath, 'utf8')
                    .split('\n')
                    .filter(line => line.trim() && !line.startsWith('#'));
                this.workspaceStructure.moduleType = 'python';
            }

            console.log('Workspace structure analyzed:', this.workspaceStructure);
        } catch (error) {
            console.error('Failed to analyze workspace structure:', error);
        }
    }

    private async loadIndexFromStorage(): Promise<void> {
        try {
            // Check version compatibility
            const storedVersion = this.context.globalState.get<string>('morpheus.indexVersion');
            if (storedVersion !== ContextEngine.INDEX_VERSION) {
                console.log(`Index version mismatch (stored: ${storedVersion}, current: ${ContextEngine.INDEX_VERSION}). Rebuilding index...`);
                await this.clearStoredIndex();
                return;
            }

            const storedIndex = this.context.globalState.get<any>('morpheus.fileIndex');
            if (storedIndex) {
                // Validate and migrate old index format
                const validatedIndex = this.validateAndMigrateIndex(storedIndex);
                this.fileIndex = new Map(Object.entries(validatedIndex));
                console.log(`Loaded ${this.fileIndex.size} files from storage`);
            }
        } catch (error) {
            console.error('Failed to load index from storage:', error);
            // Clear corrupted index and start fresh
            await this.clearStoredIndex();
        }
    }

    private validateAndMigrateIndex(storedIndex: any): any {
        const migratedIndex: any = {};

        for (const [filePath, fileIndex] of Object.entries(storedIndex)) {
            try {
                const typedFileIndex = fileIndex as any;

                // Ensure all required properties exist with proper types
                const migratedFileIndex: FileIndex = {
                    filePath: typedFileIndex.filePath || filePath,
                    lastModified: typedFileIndex.lastModified || 0,
                    symbols: Array.isArray(typedFileIndex.symbols) ? typedFileIndex.symbols : [],
                    content: typedFileIndex.content || '',
                    language: typedFileIndex.language || 'unknown',
                    imports: Array.isArray(typedFileIndex.imports) ? typedFileIndex.imports : [],
                    exports: Array.isArray(typedFileIndex.exports) ? typedFileIndex.exports : [],
                    dependencies: Array.isArray(typedFileIndex.dependencies) ? typedFileIndex.dependencies : [],
                    projectStructure: typedFileIndex.projectStructure || undefined
                };

                // Validate symbols array
                migratedFileIndex.symbols = migratedFileIndex.symbols.map((symbol: any) => ({
                    name: symbol.name || 'unknown',
                    type: symbol.type || 'variable',
                    filePath: symbol.filePath || filePath,
                    startLine: symbol.startLine || 1,
                    endLine: symbol.endLine || 1,
                    content: symbol.content || '',
                    context: symbol.context,
                    parameters: Array.isArray(symbol.parameters) ? symbol.parameters : undefined,
                    returnType: symbol.returnType,
                    docstring: symbol.docstring,
                    scope: symbol.scope,
                    isExported: symbol.isExported || false,
                    dependencies: Array.isArray(symbol.dependencies) ? symbol.dependencies : undefined
                }));

                migratedIndex[filePath] = migratedFileIndex;
            } catch (error) {
                console.warn(`Failed to migrate file index for ${filePath}:`, error);
                // Skip corrupted entries
            }
        }

        return migratedIndex;
    }

    private async clearStoredIndex(): Promise<void> {
        try {
            await this.context.globalState.update('morpheus.fileIndex', undefined);
            await this.context.globalState.update('morpheus.indexVersion', undefined);
            console.log('Cleared stored index from storage');
        } catch (error) {
            console.error('Failed to clear stored index:', error);
        }
    }

    private async saveIndexToStorage(): Promise<void> {
        try {
            const indexObject = Object.fromEntries(this.fileIndex);
            await this.context.globalState.update('morpheus.fileIndex', indexObject);
            await this.context.globalState.update('morpheus.indexVersion', ContextEngine.INDEX_VERSION);
        } catch (error) {
            console.error('Failed to save index to storage:', error);
        }
    }

    private startFileWatcher(): void {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            return;
        }

        const watchPatterns = [
            '**/*.py',
            '**/*.vue',
            '**/*.ts',
            '**/*.js'
        ];

        this.watcher = chokidar.watch(watchPatterns, {
            cwd: workspaceFolder.uri.fsPath,
            ignored: (filePath: string) => this.securityManager.isFileIgnored(filePath),
            persistent: true,
            ignoreInitial: true
        });

        this.watcher.on('add', (filePath) => this.handleFileChange(filePath, 'add'));
        this.watcher.on('change', (filePath) => this.handleFileChange(filePath, 'change'));
        this.watcher.on('unlink', (filePath) => this.handleFileChange(filePath, 'delete'));

        console.log('File watcher started');
    }

    private async handleFileChange(relativePath: string, changeType: 'add' | 'change' | 'delete'): Promise<void> {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            return;
        }

        const fullPath = path.join(workspaceFolder.uri.fsPath, relativePath);

        switch (changeType) {
            case 'add':
            case 'change':
                await this.indexFile(fullPath);
                break;
            case 'delete':
                this.fileIndex.delete(fullPath);
                break;
        }

        // Save updated index
        await this.saveIndexToStorage();
    }

    private async performInitialIndexing(): Promise<void> {
        if (this.isIndexing) {
            console.log('Indexing already in progress, skipping...');
            return;
        }

        this.isIndexing = true;
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            console.log('No workspace folder found, skipping indexing');
            this.isIndexing = false;
            return;
        }

        console.log(`Starting initial indexing of workspace: ${workspaceFolder.uri.fsPath}`);

        try {
            await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: 'Morpheus: Indexing workspace...',
                cancellable: false
            }, async (progress) => {
                const files = await this.findSupportedFiles(workspaceFolder.uri.fsPath);
                console.log(`Found ${files.length} supported files to index`);

                if (files.length === 0) {
                    console.warn('No supported files found in workspace!');
                    vscode.window.showWarningMessage('Morpheus: No supported files found in workspace. Supported extensions: .py, .vue, .ts, .js, .jsx, .tsx, .json, .md');
                }

                let processed = 0;
                let indexed = 0;

                for (const filePath of files) {
                    console.log(`Processing file ${processed + 1}/${files.length}: ${filePath}`);

                    if (this.securityManager.isFileSafeToProcess(filePath)) {
                        try {
                            await this.indexFile(filePath);
                            indexed++;
                            console.log(`Successfully indexed: ${filePath}`);
                        } catch (error) {
                            console.error(`Failed to index file ${filePath}:`, error);
                        }
                    } else {
                        console.log(`File not safe to process: ${filePath}`);
                    }

                    processed++;
                    progress.report({
                        increment: (processed / files.length) * 100,
                        message: `Processed ${processed}/${files.length} files (${indexed} indexed)`
                    });
                }

                console.log(`Indexing complete: ${indexed}/${processed} files successfully indexed`);
            });

            await this.saveIndexToStorage();
            console.log(`Final index size: ${this.fileIndex.size} files`);

            if (this.fileIndex.size > 0) {
                vscode.window.showInformationMessage(`Morpheus: Successfully indexed ${this.fileIndex.size} files`);
            } else {
                vscode.window.showWarningMessage('Morpheus: No files were indexed. Check console for details.');
            }
        } catch (error) {
            console.error('Failed to perform initial indexing:', error);
            vscode.window.showErrorMessage(`Morpheus indexing failed: ${error instanceof Error ? error.message : String(error)}`);
        } finally {
            this.isIndexing = false;
        }
    }

    private async findSupportedFiles(rootPath: string): Promise<string[]> {
        const files: string[] = [];
        const extensions = ['.py', '.vue', '.ts', '.js', '.jsx', '.tsx', '.mjs', '.cjs', '.json', '.md', '.txt', '.yml', '.yaml', '.toml', '.ini', '.cfg'];

        console.log(`Scanning workspace: ${rootPath}`);
        console.log(`Looking for extensions: ${extensions.join(', ')}`);

        const walkDir = (dir: string, depth: number = 0) => {
            try {
                // Prevent infinite recursion and skip very deep directories
                if (depth > 10) {
                    console.log(`Skipping deep directory: ${dir} (depth: ${depth})`);
                    return;
                }

                const items = fs.readdirSync(dir);
                console.log(`Scanning directory: ${dir} (${items.length} items, depth: ${depth})`);

                for (const item of items) {
                    const fullPath = path.join(dir, item);

                    // Skip common directories that should be ignored
                    if (this.shouldSkipDirectory(item, fullPath)) {
                        console.log(`Skipping directory: ${fullPath}`);
                        continue;
                    }

                    if (this.securityManager.isFileIgnored(fullPath)) {
                        console.log(`File ignored by security manager: ${fullPath}`);
                        continue;
                    }

                    const stat = fs.statSync(fullPath);
                    if (stat.isDirectory()) {
                        walkDir(fullPath, depth + 1);
                    } else {
                        const ext = path.extname(fullPath).toLowerCase();
                        if (extensions.includes(ext)) {
                            files.push(fullPath);
                            console.log(`Found supported file: ${fullPath}`);
                        } else {
                            console.log(`Skipping unsupported file: ${fullPath} (extension: ${ext})`);
                        }
                    }
                }
            } catch (error) {
                console.error(`Error walking directory ${dir}:`, error);
            }
        };

        walkDir(rootPath);
        console.log(`Total files found: ${files.length}`);
        return files;
    }

    private shouldSkipDirectory(dirName: string, fullPath: string): boolean {
        const skipDirs = [
            'node_modules', '.git', '.vscode', '.idea', '__pycache__',
            '.pytest_cache', 'dist', 'build', 'out', '.next', '.nuxt',
            'coverage', '.nyc_output', 'target', 'bin', 'obj', '.vs',
            'venv', 'env', '.env', 'virtualenv', '.venv'
        ];

        return skipDirs.includes(dirName.toLowerCase()) ||
               dirName.startsWith('.') && dirName !== '.vscode';
    }

    private async indexFile(filePath: string): Promise<void> {
        try {
            if (!this.securityManager.isFileSafeToProcess(filePath)) {
                return;
            }

            const stats = fs.statSync(filePath);
            const existingIndex = this.fileIndex.get(filePath);

            // Skip if file hasn't been modified
            if (existingIndex && existingIndex.lastModified >= stats.mtime.getTime()) {
                return;
            }

            const content = fs.readFileSync(filePath, 'utf8');
            const sanitizedContent = this.securityManager.sanitizeContent(content);
            const language = this.getLanguageFromExtension(path.extname(filePath));
            
            if (!language) {
                return;
            }

            const symbols = await this.extractSymbols(sanitizedContent, language, filePath);
            const imports = this.extractImports(sanitizedContent, language);
            const exports = this.extractExports(sanitizedContent, language);
            const dependencies = this.extractDependencies(sanitizedContent, language);

            const fileIndex: FileIndex = {
                filePath,
                lastModified: stats.mtime.getTime(),
                symbols,
                content: sanitizedContent,
                language,
                imports,
                exports,
                dependencies,
                projectStructure: this.workspaceStructure || undefined
            };

            this.fileIndex.set(filePath, fileIndex);
        } catch (error) {
            console.error(`Failed to index file ${filePath}:`, error);
        }
    }

    private getLanguageFromExtension(extension: string): string | null {
        const mapping: { [key: string]: string } = {
            '.py': 'python',
            '.ts': 'typescript',
            '.js': 'javascript',
            '.vue': 'vue'
        };
        return mapping[extension] || null;
    }

    private extractImports(content: string, language: string): string[] {
        const imports: string[] = [];
        const lines = content.split('\n');

        switch (language) {
            case 'python':
                for (const line of lines) {
                    const trimmed = line.trim();
                    // import module
                    const importMatch = trimmed.match(/^import\s+([^\s#]+)/);
                    if (importMatch) {
                        imports.push(importMatch[1]);
                    }
                    // from module import ...
                    const fromMatch = trimmed.match(/^from\s+([^\s#]+)\s+import/);
                    if (fromMatch) {
                        imports.push(fromMatch[1]);
                    }
                }
                break;

            case 'typescript':
            case 'javascript':
                for (const line of lines) {
                    const trimmed = line.trim();
                    // import ... from 'module'
                    const importMatch = trimmed.match(/import.*from\s+['"`]([^'"`]+)['"`]/);
                    if (importMatch) {
                        imports.push(importMatch[1]);
                    }
                    // const ... = require('module')
                    const requireMatch = trimmed.match(/require\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/);
                    if (requireMatch) {
                        imports.push(requireMatch[1]);
                    }
                }
                break;

            case 'vue':
                // Extract imports from script section
                const scriptMatch = content.match(/<script[^>]*>([\s\S]*?)<\/script>/);
                if (scriptMatch) {
                    return this.extractImports(scriptMatch[1], 'javascript');
                }
                break;
        }

        return imports;
    }

    private extractExports(content: string, language: string): string[] {
        const exports: string[] = [];
        const lines = content.split('\n');

        switch (language) {
            case 'python':
                // Look for __all__ definition
                const allMatch = content.match(/__all__\s*=\s*\[(.*?)\]/s);
                if (allMatch) {
                    const items = allMatch[1].match(/'([^']+)'|"([^"]+)"/g);
                    if (items) {
                        exports.push(...items.map(item => item.slice(1, -1)));
                    }
                }
                // Also look for top-level functions and classes
                for (const line of lines) {
                    const trimmed = line.trim();
                    if (trimmed.match(/^(def|class)\s+\w+/) && !trimmed.startsWith('_')) {
                        const match = trimmed.match(/^(def|class)\s+(\w+)/);
                        if (match) {
                            exports.push(match[2]);
                        }
                    }
                }
                break;

            case 'typescript':
            case 'javascript':
                for (const line of lines) {
                    const trimmed = line.trim();
                    // export function/class/const
                    const exportMatch = trimmed.match(/export\s+(?:function|class|const|let|var)\s+(\w+)/);
                    if (exportMatch) {
                        exports.push(exportMatch[1]);
                    }
                    // export { ... }
                    const exportBraceMatch = trimmed.match(/export\s*{\s*([^}]+)\s*}/);
                    if (exportBraceMatch) {
                        const items = exportBraceMatch[1].split(',').map(item => item.trim());
                        exports.push(...items);
                    }
                }
                break;
        }

        return exports;
    }

    private extractDependencies(content: string, language: string): string[] {
        const dependencies: string[] = [];

        // Extract function calls, method calls, and variable references
        switch (language) {
            case 'python':
                // Look for function calls and method calls
                const pythonCalls = content.match(/(\w+)(?:\.\w+)*\s*\(/g);
                if (pythonCalls) {
                    dependencies.push(...pythonCalls.map(call => call.replace(/\s*\($/, '')));
                }
                break;

            case 'typescript':
            case 'javascript':
                // Look for function calls and method calls
                const jsCalls = content.match(/(\w+)(?:\.\w+)*\s*\(/g);
                if (jsCalls) {
                    dependencies.push(...jsCalls.map(call => call.replace(/\s*\($/, '')));
                }
                break;
        }

        // Remove duplicates and common keywords
        const filtered = [...new Set(dependencies)].filter(dep =>
            !['if', 'for', 'while', 'function', 'class', 'return', 'console', 'log'].includes(dep)
        );

        return filtered.slice(0, 20); // Limit to prevent noise
    }

    private async extractSymbols(content: string, language: string, filePath: string): Promise<Symbol[]> {
        // Use regex-based parsing as fallback for tree-sitter issues
        try {
            return this.extractSymbolsWithRegex(content, language, filePath);
        } catch (error) {
            console.error(`Failed to parse ${filePath}:`, error);
            return [];
        }
    }

    private extractSymbolsWithRegex(content: string, language: string, filePath: string): Symbol[] {
        const symbols: Symbol[] = [];
        const lines = content.split('\n');

        switch (language) {
            case 'python':
                this.extractPythonSymbols(lines, filePath, symbols);
                break;
            case 'typescript':
            case 'javascript':
                this.extractJSSymbols(lines, filePath, symbols);
                break;
            case 'vue':
                this.extractVueSymbols(lines, filePath, symbols);
                break;
        }

        return symbols;
    }

    private extractPythonSymbols(lines: string[], filePath: string, symbols: Symbol[]): void {
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();

            // Extract functions with parameters and docstrings
            const funcMatch = line.match(/^def\s+(\w+)\s*\(([^)]*)\)/);
            if (funcMatch) {
                const parameters = funcMatch[2] ? funcMatch[2].split(',').map(p => p.trim()) : [];
                const docstring = this.extractPythonDocstring(lines, i + 1);

                symbols.push({
                    name: funcMatch[1],
                    type: 'function',
                    filePath,
                    startLine: i + 1,
                    endLine: this.findPythonBlockEnd(lines, i),
                    content: this.extractPythonBlock(lines, i),
                    context: this.getContext(lines, i, this.findPythonBlockEnd(lines, i)),
                    parameters,
                    docstring,
                    isExported: !funcMatch[1].startsWith('_')
                });
            }

            // Extract classes with inheritance
            const classMatch = line.match(/^class\s+(\w+)(?:\(([^)]*)\))?/);
            if (classMatch) {
                const inheritance = classMatch[2] ? classMatch[2].split(',').map(p => p.trim()) : [];
                const docstring = this.extractPythonDocstring(lines, i + 1);

                symbols.push({
                    name: classMatch[1],
                    type: 'class',
                    filePath,
                    startLine: i + 1,
                    endLine: this.findPythonBlockEnd(lines, i),
                    content: this.extractPythonBlock(lines, i),
                    context: this.getContext(lines, i, this.findPythonBlockEnd(lines, i)),
                    dependencies: inheritance,
                    docstring,
                    isExported: !classMatch[1].startsWith('_')
                });
            }

            // Extract variables with type hints
            const varMatch = line.match(/^(\w+)\s*:\s*([^=]+)(?:\s*=.*)?/) || line.match(/^(\w+)\s*=\s*(.+)/);
            if (varMatch && !line.includes('def ') && !line.includes('class ')) {
                symbols.push({
                    name: varMatch[1],
                    type: 'variable',
                    filePath,
                    startLine: i + 1,
                    endLine: i + 1,
                    content: line,
                    context: this.getContext(lines, i, i),
                    returnType: varMatch[2] ? varMatch[2].trim() : undefined,
                    isExported: !varMatch[1].startsWith('_')
                });
            }

            // Extract method definitions inside classes
            if (line.match(/^\s+def\s+(\w+)/)) {
                const methodMatch = line.match(/^\s+def\s+(\w+)\s*\(([^)]*)\)/);
                if (methodMatch) {
                    const parameters = methodMatch[2] ? methodMatch[2].split(',').map(p => p.trim()) : [];
                    const docstring = this.extractPythonDocstring(lines, i + 1);

                    symbols.push({
                        name: methodMatch[1],
                        type: 'method',
                        filePath,
                        startLine: i + 1,
                        endLine: this.findPythonBlockEnd(lines, i),
                        content: this.extractPythonBlock(lines, i),
                        context: this.getContext(lines, i, this.findPythonBlockEnd(lines, i)),
                        parameters,
                        docstring,
                        scope: 'class',
                        isExported: !methodMatch[1].startsWith('_')
                    });
                }
            }
        }
    }

    private extractPythonDocstring(lines: string[], startLine: number): string | undefined {
        if (startLine >= lines.length) return undefined;

        const line = lines[startLine].trim();
        if (line.startsWith('"""') || line.startsWith("'''")) {
            const quote = line.startsWith('"""') ? '"""' : "'''";

            // Single line docstring
            if (line.endsWith(quote) && line.length > 6) {
                return line.slice(3, -3).trim();
            }

            // Multi-line docstring
            let docstring = line.slice(3);
            for (let i = startLine + 1; i < lines.length; i++) {
                const docLine = lines[i];
                if (docLine.includes(quote)) {
                    docstring += '\n' + docLine.substring(0, docLine.indexOf(quote));
                    break;
                }
                docstring += '\n' + docLine;
            }
            return docstring.trim();
        }

        return undefined;
    }

    private extractJSSymbols(lines: string[], filePath: string, symbols: Symbol[]): void {
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();

            // Extract functions
            const funcMatch = line.match(/(?:function\s+(\w+)|(?:const|let|var)\s+(\w+)\s*=\s*(?:function|\([^)]*\)\s*=>))/);
            if (funcMatch) {
                const name = funcMatch[1] || funcMatch[2];
                symbols.push({
                    name,
                    type: 'function',
                    filePath,
                    startLine: i + 1,
                    endLine: this.findJSBlockEnd(lines, i),
                    content: this.extractJSBlock(lines, i),
                    context: this.getContext(lines, i, this.findJSBlockEnd(lines, i))
                });
            }

            // Extract classes
            const classMatch = line.match(/class\s+(\w+)/);
            if (classMatch) {
                symbols.push({
                    name: classMatch[1],
                    type: 'class',
                    filePath,
                    startLine: i + 1,
                    endLine: this.findJSBlockEnd(lines, i),
                    content: this.extractJSBlock(lines, i),
                    context: this.getContext(lines, i, this.findJSBlockEnd(lines, i))
                });
            }

            // Extract variables
            const varMatch = line.match(/(?:const|let|var)\s+(\w+)/);
            if (varMatch && !line.includes('function') && !line.includes('=>')) {
                symbols.push({
                    name: varMatch[1],
                    type: 'variable',
                    filePath,
                    startLine: i + 1,
                    endLine: i + 1,
                    content: line,
                    context: this.getContext(lines, i, i)
                });
            }
        }
    }

    private extractVueSymbols(lines: string[], filePath: string, symbols: Symbol[]): void {
        let inScript = false;
        let scriptContent = '';

        // First, extract the script content
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];

            if (line.includes('<script')) {
                inScript = true;
                continue;
            }
            if (line.includes('</script>')) {
                inScript = false;
                break;
            }

            if (inScript) {
                scriptContent += line + '\n';
            }
        }

        // Parse the script content as JavaScript to extract Vue-specific symbols
        const scriptLines = scriptContent.split('\n');

        for (let i = 0; i < scriptLines.length; i++) {
            const line = scriptLines[i].trim();

            // Extract component name
            const nameMatch = line.match(/name:\s*['"`](\w+)['"`]/);
            if (nameMatch) {
                symbols.push({
                    name: nameMatch[1],
                    type: 'class',
                    filePath,
                    startLine: i + 1,
                    endLine: i + 1,
                    content: line,
                    context: 'Vue component name'
                });
            }

            // Extract methods from methods object
            const methodMatch = line.match(/(\w+)\s*\([^)]*\)\s*{/) || line.match(/(\w+)\s*:\s*(?:async\s+)?function/);
            if (methodMatch) {
                symbols.push({
                    name: methodMatch[1],
                    type: 'method',
                    filePath,
                    startLine: i + 1,
                    endLine: this.findJSBlockEnd(scriptLines, i),
                    content: this.extractJSBlock(scriptLines, i),
                    context: 'Vue component method'
                });
            }

            // Extract data properties
            const dataMatch = line.match(/(\w+):\s*(.+),?\s*$/);
            if (dataMatch && !line.includes('function') && !line.includes('=>') && !line.includes('name:')) {
                symbols.push({
                    name: dataMatch[1],
                    type: 'property',
                    filePath,
                    startLine: i + 1,
                    endLine: i + 1,
                    content: line,
                    context: 'Vue component property'
                });
            }

            // Extract computed properties
            if (line.includes('computed:')) {
                // Look for computed properties in the following lines
                for (let j = i + 1; j < scriptLines.length && j < i + 20; j++) {
                    const computedLine = scriptLines[j].trim();
                    const computedMatch = computedLine.match(/(\w+)\s*\([^)]*\)\s*{/) || computedLine.match(/(\w+)\s*:\s*function/);
                    if (computedMatch) {
                        symbols.push({
                            name: computedMatch[1],
                            type: 'method',
                            filePath,
                            startLine: j + 1,
                            endLine: this.findJSBlockEnd(scriptLines, j),
                            content: this.extractJSBlock(scriptLines, j),
                            context: 'Vue computed property'
                        });
                    }
                }
            }
        }

        // Always add a default symbol for the Vue component itself if no name was found
        if (!symbols.some(s => s.type === 'class')) {
            const componentName = path.basename(filePath, '.vue');
            symbols.push({
                name: componentName,
                type: 'class',
                filePath,
                startLine: 1,
                endLine: lines.length,
                content: 'Vue component',
                context: 'Vue component file'
            });
        }
    }

    private findPythonBlockEnd(lines: string[], startLine: number): number {
        const startIndent = lines[startLine].length - lines[startLine].trimStart().length;

        for (let i = startLine + 1; i < lines.length; i++) {
            const line = lines[i];
            if (line.trim() === '') continue;

            const indent = line.length - line.trimStart().length;
            if (indent <= startIndent && line.trim() !== '') {
                return i - 1;
            }
        }
        return lines.length - 1;
    }

    private findJSBlockEnd(lines: string[], startLine: number): number {
        let braceCount = 0;
        let foundOpenBrace = false;

        for (let i = startLine; i < lines.length; i++) {
            const line = lines[i];
            for (const char of line) {
                if (char === '{') {
                    braceCount++;
                    foundOpenBrace = true;
                } else if (char === '}') {
                    braceCount--;
                    if (foundOpenBrace && braceCount === 0) {
                        return i + 1;
                    }
                }
            }
        }
        return startLine + 1;
    }

    private extractPythonBlock(lines: string[], startLine: number): string {
        const endLine = this.findPythonBlockEnd(lines, startLine);
        return lines.slice(startLine, endLine + 1).join('\n');
    }

    private extractJSBlock(lines: string[], startLine: number): string {
        const endLine = this.findJSBlockEnd(lines, startLine);
        return lines.slice(startLine, endLine + 1).join('\n');
    }

    private getContext(lines: string[], startLine: number, endLine: number): string {
        const contextBefore = Math.max(0, startLine - 3);
        const contextAfter = Math.min(lines.length - 1, endLine + 3);
        return lines.slice(contextBefore, contextAfter + 1).join('\n');
    }

    private buildSymbolGraph(): void {
        this.symbolGraph.clear();

        try {
            for (const fileIndex of this.fileIndex.values()) {
                try {
                    // Ensure symbols is an array
                    const symbols = Array.isArray(fileIndex.symbols) ? fileIndex.symbols : [];

                    for (const symbol of symbols) {
                        if (symbol && symbol.name) {
                            if (!this.symbolGraph.has(symbol.name)) {
                                this.symbolGraph.set(symbol.name, new Set());
                            }
                            this.symbolGraph.get(symbol.name)!.add(fileIndex.filePath);

                            // Add dependencies to the graph
                            if (Array.isArray(symbol.dependencies)) {
                                for (const dep of symbol.dependencies) {
                                    if (dep && typeof dep === 'string') {
                                        if (!this.symbolGraph.has(dep)) {
                                            this.symbolGraph.set(dep, new Set());
                                        }
                                        this.symbolGraph.get(dep)!.add(fileIndex.filePath);
                                    }
                                }
                            }
                        }
                    }

                    // Add imports to the graph
                    const imports = Array.isArray(fileIndex.imports) ? fileIndex.imports : [];
                    for (const importName of imports) {
                        if (importName && typeof importName === 'string') {
                            if (!this.symbolGraph.has(importName)) {
                                this.symbolGraph.set(importName, new Set());
                            }
                            this.symbolGraph.get(importName)!.add(fileIndex.filePath);
                        }
                    }
                } catch (error) {
                    console.warn(`Failed to process file index for ${fileIndex.filePath}:`, error);
                }
            }

            console.log(`Built symbol graph with ${this.symbolGraph.size} symbols`);
        } catch (error) {
            console.error('Failed to build symbol graph:', error);
            this.symbolGraph.clear();
        }
    }

    // Public API methods
    public async refreshIndex(): Promise<void> {
        this.fileIndex.clear();
        await this.performInitialIndexing();
        this.buildSymbolGraph();
    }

    public getSymbolsInFile(filePath: string): Symbol[] {
        const fileIndex = this.fileIndex.get(filePath);
        return fileIndex ? fileIndex.symbols : [];
    }

    public searchSymbols(query: string, type?: string): Symbol[] {
        const results: Symbol[] = [];
        const queryLower = query.toLowerCase();

        for (const fileIndex of this.fileIndex.values()) {
            for (const symbol of fileIndex.symbols) {
                if (type && symbol.type !== type) {
                    continue;
                }
                
                if (symbol.name.toLowerCase().includes(queryLower) ||
                    symbol.content.toLowerCase().includes(queryLower)) {
                    results.push(symbol);
                }
            }
        }

        return results.sort((a, b) => {
            // Prioritize exact matches
            const aExact = a.name.toLowerCase() === queryLower ? 1 : 0;
            const bExact = b.name.toLowerCase() === queryLower ? 1 : 0;
            return bExact - aExact;
        });
    }

    public getRelevantContext(currentFile: string, maxTokens: number = 200000): string {
        const contexts: string[] = [];
        let tokenCount = 0;

        // Add current file context first
        const currentFileIndex = this.fileIndex.get(currentFile);
        if (currentFileIndex) {
            const fileHeader = `// File: ${path.relative(vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '', currentFile)}`;
            const symbolSummary = this.generateSymbolSummary(currentFileIndex);
            contexts.push(`${fileHeader}\n${symbolSummary}\n${currentFileIndex.content}`);
            tokenCount += (symbolSummary.length + currentFileIndex.content.length) / 4;
        }

        // Get related files with better scoring
        const relatedFiles = this.getRelatedFiles(currentFile, currentFileIndex);

        for (const { fileIndex, score } of relatedFiles) {
            if (tokenCount >= maxTokens) {
                break;
            }

            const fileHeader = `// File: ${path.relative(vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '', fileIndex.filePath)} (relevance: ${score.toFixed(2)})`;
            const symbolSummary = this.generateSymbolSummary(fileIndex);

            // For highly relevant files, include full content
            if (score > 0.7) {
                const fileContext = `${fileHeader}\n${symbolSummary}\n${fileIndex.content}`;
                contexts.push(fileContext);
                tokenCount += (symbolSummary.length + fileIndex.content.length) / 4;
            } else {
                // For less relevant files, include only symbol definitions
                contexts.push(`${fileHeader}\n${symbolSummary}`);
                tokenCount += symbolSummary.length / 4;
            }
        }

        return contexts.join('\n\n');
    }

    private generateSymbolSummary(fileIndex: FileIndex): string {
        const summary: string[] = [];

        if (fileIndex.imports.length > 0) {
            summary.push(`// Imports: ${fileIndex.imports.join(', ')}`);
        }

        if (fileIndex.exports.length > 0) {
            summary.push(`// Exports: ${fileIndex.exports.join(', ')}`);
        }

        const symbolsByType = fileIndex.symbols.reduce((acc, symbol) => {
            if (!acc[symbol.type]) acc[symbol.type] = [];
            acc[symbol.type].push(symbol);
            return acc;
        }, {} as Record<string, Symbol[]>);

        for (const [type, symbols] of Object.entries(symbolsByType)) {
            const symbolNames = symbols.map(s => {
                let name = s.name;
                if (s.parameters && s.parameters.length > 0) {
                    name += `(${s.parameters.join(', ')})`;
                }
                if (s.returnType) {
                    name += `: ${s.returnType}`;
                }
                return name;
            });
            summary.push(`// ${type}s: ${symbolNames.join(', ')}`);
        }

        return summary.join('\n');
    }

    private getRelatedFiles(currentFile: string, currentFileIndex: FileIndex | undefined): Array<{fileIndex: FileIndex, score: number}> {
        const relatedFiles: Array<{fileIndex: FileIndex, score: number}> = [];

        for (const fileIndex of this.fileIndex.values()) {
            if (fileIndex.filePath === currentFile) {
                continue;
            }

            const score = this.calculateRelevanceScore(currentFileIndex, fileIndex);
            if (score > 0.1) {
                relatedFiles.push({ fileIndex, score });
            }
        }

        return relatedFiles.sort((a, b) => b.score - a.score).slice(0, 10); // Top 10 most relevant files
    }

    private calculateRelevanceScore(currentFile: FileIndex | undefined, otherFile: FileIndex): number {
        if (!currentFile) {
            return 0;
        }

        let score = 0;

        // Same directory bonus (stronger for closer directories)
        const currentDir = path.dirname(currentFile.filePath);
        const otherDir = path.dirname(otherFile.filePath);
        const relativePath = path.relative(currentDir, otherDir);

        if (currentDir === otherDir) {
            score += 0.4; // Same directory
        } else if (relativePath === '..') {
            score += 0.2; // Parent directory
        } else if (!relativePath.includes('..')) {
            score += 0.1; // Subdirectory
        }

        // Same language bonus
        if (currentFile.language === otherFile.language) {
            score += 0.3;
        }

        // Import/export relationships (very important)
        for (const importName of currentFile.imports) {
            if (otherFile.exports.includes(importName)) {
                score += 0.5; // Direct import relationship
            }
            if (otherFile.filePath.includes(importName) || importName.includes(path.basename(otherFile.filePath, path.extname(otherFile.filePath)))) {
                score += 0.3; // Likely import relationship
            }
        }

        // Symbol reference bonus (bidirectional)
        const currentSymbolNames = new Set(currentFile.symbols.map(s => s.name));
        const otherSymbolNames = new Set(otherFile.symbols.map(s => s.name));

        for (const symbol of currentFile.symbols) {
            if (otherFile.content.includes(symbol.name)) {
                score += 0.15;
            }
        }

        for (const symbol of otherFile.symbols) {
            if (currentFile.content.includes(symbol.name)) {
                score += 0.15;
            }
        }

        // Dependency relationships
        for (const dep of currentFile.dependencies) {
            if (otherSymbolNames.has(dep)) {
                score += 0.2;
            }
        }

        // File naming patterns (e.g., test files, config files)
        const currentBasename = path.basename(currentFile.filePath, path.extname(currentFile.filePath));
        const otherBasename = path.basename(otherFile.filePath, path.extname(otherFile.filePath));

        if (otherBasename.includes(currentBasename) || currentBasename.includes(otherBasename)) {
            score += 0.2;
        }

        // Test file relationships
        if ((currentBasename.includes('test') && !otherBasename.includes('test')) ||
            (!currentBasename.includes('test') && otherBasename.includes('test'))) {
            if (otherBasename.includes(currentBasename.replace(/\.test$/, '')) ||
                currentBasename.includes(otherBasename.replace(/\.test$/, ''))) {
                score += 0.3;
            }
        }

        return Math.min(score, 1.0);
    }

    public getSymbolsForCompletion(currentFile: string, prefix: string): Symbol[] {
        const results: Symbol[] = [];
        const prefixLower = prefix.toLowerCase();

        // Get symbols from current file first
        const currentFileIndex = this.fileIndex.get(currentFile);
        if (currentFileIndex) {
            for (const symbol of currentFileIndex.symbols) {
                if (symbol.name.toLowerCase().startsWith(prefixLower)) {
                    results.push(symbol);
                }
            }
        }

        // Get symbols from imported files
        if (currentFileIndex) {
            for (const importName of currentFileIndex.imports) {
                const relatedFiles = this.symbolGraph.get(importName);
                if (relatedFiles) {
                    for (const filePath of relatedFiles) {
                        const fileIndex = this.fileIndex.get(filePath);
                        if (fileIndex && fileIndex.filePath !== currentFile) {
                            for (const symbol of fileIndex.symbols) {
                                if (symbol.isExported && symbol.name.toLowerCase().startsWith(prefixLower)) {
                                    results.push(symbol);
                                }
                            }
                        }
                    }
                }
            }
        }

        // Sort by relevance
        return results.sort((a, b) => {
            // Exact matches first
            if (a.name.toLowerCase() === prefixLower && b.name.toLowerCase() !== prefixLower) return -1;
            if (b.name.toLowerCase() === prefixLower && a.name.toLowerCase() !== prefixLower) return 1;

            // Current file symbols first
            if (a.filePath === currentFile && b.filePath !== currentFile) return -1;
            if (b.filePath === currentFile && a.filePath !== currentFile) return 1;

            // Exported symbols first
            if (a.isExported && !b.isExported) return -1;
            if (b.isExported && !a.isExported) return 1;

            // Alphabetical
            return a.name.localeCompare(b.name);
        }).slice(0, 50); // Limit results
    }

    public getSymbolDefinition(symbolName: string, currentFile?: string): Symbol | undefined {
        // First check current file if provided
        if (currentFile) {
            const currentFileIndex = this.fileIndex.get(currentFile);
            if (currentFileIndex) {
                const symbol = currentFileIndex.symbols.find(s => s.name === symbolName);
                if (symbol) return symbol;
            }
        }

        // Then check all files
        for (const fileIndex of this.fileIndex.values()) {
            const symbol = fileIndex.symbols.find(s => s.name === symbolName && s.isExported);
            if (symbol) return symbol;
        }

        return undefined;
    }

    public getWorkspaceStructure(): ProjectStructure | null {
        return this.workspaceStructure;
    }

    public getFileIndex(filePath: string): FileIndex | undefined {
        return this.fileIndex.get(filePath);
    }

    public getAllSymbols(): Symbol[] {
        const allSymbols: Symbol[] = [];
        for (const fileIndex of this.fileIndex.values()) {
            allSymbols.push(...fileIndex.symbols);
        }
        return allSymbols;
    }

    public getSymbolReferences(symbolName: string): string[] {
        const references: string[] = [];
        const symbolFiles = this.symbolGraph.get(symbolName);
        if (symbolFiles) {
            references.push(...Array.from(symbolFiles));
        }
        return references;
    }

    public getIndexedFileCount(): number {
        return this.fileIndex.size;
    }

    public dispose(): void {
        if (this.watcher) {
            this.watcher.close();
        }
        this.saveIndexToStorage();
    }
}
